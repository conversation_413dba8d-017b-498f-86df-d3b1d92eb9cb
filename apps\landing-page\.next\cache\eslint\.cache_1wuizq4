[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts": "23", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts": "24", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts": "25"}, {"size": 14710, "mtime": 1749600836091, "results": "26", "hashOfConfig": "27"}, {"size": 9016, "mtime": 1749596942009, "results": "28", "hashOfConfig": "27"}, {"size": 385, "mtime": 1749589496137, "results": "29", "hashOfConfig": "27"}, {"size": 12920, "mtime": 1749603218618, "results": "30", "hashOfConfig": "27"}, {"size": 21668, "mtime": 1749600046512, "results": "31", "hashOfConfig": "27"}, {"size": 13693, "mtime": 1749647923740, "results": "32", "hashOfConfig": "27"}, {"size": 15464, "mtime": 1749647711871, "results": "33", "hashOfConfig": "27"}, {"size": 15557, "mtime": 1749607867202, "results": "34", "hashOfConfig": "27"}, {"size": 15978, "mtime": 1749600583678, "results": "35", "hashOfConfig": "27"}, {"size": 18603, "mtime": 1749607796602, "results": "36", "hashOfConfig": "27"}, {"size": 14612, "mtime": 1749607928375, "results": "37", "hashOfConfig": "27"}, {"size": 3319, "mtime": 1749593558895, "results": "38", "hashOfConfig": "27"}, {"size": 2845, "mtime": 1749647582298, "results": "39", "hashOfConfig": "27"}, {"size": 1646, "mtime": 1749589446276, "results": "40", "hashOfConfig": "27"}, {"size": 2677, "mtime": 1749597314087, "results": "41", "hashOfConfig": "27"}, {"size": 2428, "mtime": 1749596227387, "results": "42", "hashOfConfig": "27"}, {"size": 989, "mtime": 1749596212722, "results": "43", "hashOfConfig": "27"}, {"size": 2120, "mtime": 1749597019188, "results": "44", "hashOfConfig": "27"}, {"size": 2328, "mtime": 1749607729735, "results": "45", "hashOfConfig": "27"}, {"size": 9064, "mtime": 1749647483985, "results": "46", "hashOfConfig": "27"}, {"size": 5749, "mtime": 1749647330487, "results": "47", "hashOfConfig": "27"}, {"size": 4053, "mtime": 1749647433136, "results": "48", "hashOfConfig": "27"}, {"size": 5846, "mtime": 1749647373011, "results": "49", "hashOfConfig": "27"}, {"size": 5926, "mtime": 1749647406588, "results": "50", "hashOfConfig": "27"}, {"size": 3583, "mtime": 1749647289790, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", ["127", "128", "129"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", ["130", "131"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["132"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["133", "134", "135", "136", "137", "138", "139", "140"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx", ["141", "142"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx", ["143"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts", [], [], {"ruleId": "144", "severity": 2, "message": "145", "line": 23, "column": 11, "nodeType": "146", "messageId": "147", "endLine": 23, "endColumn": 23}, {"ruleId": "144", "severity": 2, "message": "148", "line": 23, "column": 25, "nodeType": "146", "messageId": "147", "endLine": 23, "endColumn": 34}, {"ruleId": "144", "severity": 2, "message": "149", "line": 23, "column": 49, "nodeType": "146", "messageId": "147", "endLine": 23, "endColumn": 62}, {"ruleId": "144", "severity": 2, "message": "148", "line": 19, "column": 25, "nodeType": "146", "messageId": "147", "endLine": 19, "endColumn": 34}, {"ruleId": "144", "severity": 2, "message": "149", "line": 19, "column": 49, "nodeType": "146", "messageId": "147", "endLine": 19, "endColumn": 62}, {"ruleId": "150", "severity": 1, "message": "151", "line": 37, "column": 7, "nodeType": "152", "messageId": "153", "endLine": 37, "endColumn": 20, "suggestions": "154", "suppressions": "155"}, {"ruleId": "150", "severity": 1, "message": "151", "line": 7, "column": 30, "nodeType": "152", "messageId": "153", "endLine": 7, "endColumn": 43}, {"ruleId": "150", "severity": 1, "message": "151", "line": 8, "column": 29, "nodeType": "152", "messageId": "153", "endLine": 8, "endColumn": 41}, {"ruleId": "150", "severity": 1, "message": "151", "line": 37, "column": 5, "nodeType": "152", "messageId": "153", "endLine": 37, "endColumn": 18}, {"ruleId": "156", "severity": 1, "message": "157", "line": 37, "column": 31, "nodeType": "158", "messageId": "159", "endLine": 37, "endColumn": 34, "suggestions": "160"}, {"ruleId": "150", "severity": 1, "message": "151", "line": 44, "column": 5, "nodeType": "152", "messageId": "153", "endLine": 44, "endColumn": 17}, {"ruleId": "156", "severity": 1, "message": "157", "line": 44, "column": 30, "nodeType": "158", "messageId": "159", "endLine": 44, "endColumn": 33, "suggestions": "161"}, {"ruleId": "150", "severity": 1, "message": "151", "line": 73, "column": 3, "nodeType": "152", "messageId": "153", "endLine": 73, "endColumn": 16}, {"ruleId": "150", "severity": 1, "message": "151", "line": 74, "column": 3, "nodeType": "152", "messageId": "153", "endLine": 74, "endColumn": 15}, {"ruleId": "144", "severity": 2, "message": "162", "line": 11, "column": 3, "nodeType": "146", "messageId": "147", "endLine": 11, "endColumn": 16}, {"ruleId": "144", "severity": 2, "message": "148", "line": 24, "column": 11, "nodeType": "146", "messageId": "147", "endLine": 24, "endColumn": 20}, {"ruleId": "144", "severity": 2, "message": "163", "line": 8, "column": 3, "nodeType": "146", "messageId": "147", "endLine": 8, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'currentTheme' is assigned a value but never used.", "Identifier", "unusedVar", "'themeName' is assigned a value but never used.", "'isPurpleTheme' is assigned a value but never used.", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["164"], ["165"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["166", "167"], ["168", "169"], "'ChevronUpIcon' is defined but never used.", "'getStoredTheme' is defined but never used.", {"messageId": "170", "data": "171", "fix": "172", "desc": "173"}, {"kind": "174", "justification": "175"}, {"messageId": "176", "fix": "177", "desc": "178"}, {"messageId": "179", "fix": "180", "desc": "181"}, {"messageId": "176", "fix": "182", "desc": "178"}, {"messageId": "179", "fix": "183", "desc": "181"}, "removeConsole", {"propertyName": "184"}, {"range": "185", "text": "175"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "186", "text": "187"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "188", "text": "189"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "190", "text": "187"}, {"range": "191", "text": "189"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]