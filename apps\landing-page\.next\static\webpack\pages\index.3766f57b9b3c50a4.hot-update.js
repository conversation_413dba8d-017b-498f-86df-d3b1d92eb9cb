"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,SwatchIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { theme: nextTheme, setTheme: setNextTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const { currentTheme, themeName, switchTheme, isGoldTheme, isPurpleTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    // Fix hydration issue with next-themes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Navigation items\n    const navItems = [\n        {\n            key: \"features\",\n            href: \"#features\"\n        },\n        {\n            key: \"howItWorks\",\n            href: \"#how-it-works\"\n        },\n        {\n            key: \"pricing\",\n            href: \"#pricing\"\n        },\n        {\n            key: \"about\",\n            href: \"#about\"\n        },\n        {\n            key: \"contact\",\n            href: \"#contact\"\n        }\n    ];\n    // Language toggle\n    const toggleLanguage = ()=>{\n        const newLocale = locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    // Theme toggle (Next.js theme)\n    const toggleNextTheme = ()=>{\n        setNextTheme(nextTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    // Custom theme toggle (Gold/Purple)\n    const toggleCustomTheme = ()=>{\n        const newTheme = themeName === \"gold\" ? \"purple\" : \"gold\";\n        switchTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500\",\n        style: {\n            background: isScrolled ? \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary) : \"transparent\",\n            backdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            WebkitBackdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            borderBottom: isScrolled ? \"1px solid \".concat(currentTheme.colors.glass.border) : \"none\",\n            boxShadow: isScrolled ? currentTheme.colors.glass.shadow : \"none\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto container-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 rtl:space-x-reverse group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"relative w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center overflow-hidden\",\n                                    style: {\n                                        background: currentTheme.gradients.primary,\n                                        boxShadow: currentTheme.shadows.md\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BriefcaseIcon, {\n                                            className: \"w-5 h-5 lg:w-6 lg:h-6 text-white relative z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                    className: \"text-xl lg:text-2xl font-bold \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                    style: {\n                                        background: currentTheme.gradients.text,\n                                        backgroundClip: \"text\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        textShadow: \"0 2px 4px rgba(0, 0, 0, 0.3)\"\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: isRTL ? \"فريلا سوريا\" : \"Freela Syria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                    href: item.href,\n                                    className: \"relative font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                    style: {\n                                        color: currentTheme.colors.text.secondary\n                                    },\n                                    onClick: (e)=>{\n                                        var _document_querySelector;\n                                        e.preventDefault();\n                                        (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                            behavior: \"smooth\"\n                                        });\n                                    },\n                                    whileHover: {\n                                        scale: 1.05,\n                                        color: currentTheme.colors.text.accent\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: [\n                                        t(\"navigation.\".concat(item.key)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute bottom-0 left-0 h-0.5 rounded-full\",\n                                            style: {\n                                                background: currentTheme.gradients.primary\n                                            },\n                                            initial: {\n                                                width: 0\n                                            },\n                                            whileHover: {\n                                                width: \"100%\"\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleCustomTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle custom theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SwatchIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleNextTheme,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle dark/light theme\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        mounted ? nextTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    type: \"button\",\n                                    onClick: toggleLanguage,\n                                    className: \"relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse\",\n                                    style: {\n                                        background: currentTheme.colors.glass.background,\n                                        backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                        border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                                    },\n                                    \"aria-label\": \"Toggle language\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            style: {\n                                                color: currentTheme.colors.text.accent\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium relative z-10 \".concat(isRTL ? \"font-cairo\" : \"font-sans\"),\n                                            style: {\n                                                color: currentTheme.colors.text.primary\n                                            },\n                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            style: {\n                                                background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                                animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                className: \"font-medium transition-all duration-300 \".concat(isRTL ? \"font-tajawal\" : \"font-sans\"),\n                                                style: {\n                                                    color: currentTheme.colors.text.accent\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    color: currentTheme.colors.text.primary\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: t(\"navigation.login\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/signup\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                className: \"relative px-6 py-2.5 rounded-xl font-semibold overflow-hidden group \".concat(isRTL ? \"font-cairo\" : \"font-display\"),\n                                                style: {\n                                                    background: currentTheme.gradients.button,\n                                                    color: \"white\",\n                                                    boxShadow: currentTheme.shadows.md,\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: t(\"navigation.joinAsExpert\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        style: {\n                                                            background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)\",\n                                                            animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            type: \"button\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"lg:hidden relative p-2 rounded-lg overflow-hidden group\",\n                            style: {\n                                background: currentTheme.colors.glass.background,\n                                backdropFilter: currentTheme.colors.glass.backdropBlur,\n                                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,\n                                border: \"1px solid \".concat(currentTheme.colors.glass.border)\n                            },\n                            \"aria-label\": \"Toggle menu\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    animate: {\n                                        rotate: isMenuOpen ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.XMarkIcon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.Bars3Icon, {\n                                        className: \"w-6 h-6 relative z-10\",\n                                        style: {\n                                            color: currentTheme.colors.text.accent\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    style: {\n                                        background: \"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)\",\n                                        animation: \"glassShimmer 1.5s ease-in-out infinite\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"lg:hidden overflow-hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4 space-y-4\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.href,\n                                        className: \"block text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-200\",\n                                        onClick: (e)=>{\n                                            var _document_querySelector;\n                                            e.preventDefault();\n                                            setIsMenuOpen(false);\n                                            (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        children: t(\"navigation.\".concat(item.key))\n                                    }, item.key, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleTheme,\n                                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200\",\n                                                    children: mounted ? theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleLanguage,\n                                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 flex items-center space-x-1 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_SwatchIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.LanguageIcon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"navigation.login\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/signup\",\n                                                    className: \"btn-primary text-sm px-4 py-2\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"navigation.joinAsExpert\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"P+mAE4n+cOWIcESINwHy1Je9JPo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme,\n        _themes__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Header.tsx\n"));

/***/ })

});