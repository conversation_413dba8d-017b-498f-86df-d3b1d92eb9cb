"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_13_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_13_use_2_themes_theme_base_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! -!../../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./themes/theme-base.css */ \"../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/themes/theme-base.css\");\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n___CSS_LOADER_EXPORT___.i(_node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_13_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_13_use_2_themes_theme_base_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: var(--font-inter), Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: JetBrains Mono, monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  html {\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n    background: var(--primary-bg);\\n    color: var(--text-primary);\\n}\\n  \\n  /* Enhanced Arabic font optimization */\\n  .font-arabic {\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .font-cairo {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n  }\\n\\n  .font-tajawal {\\n    font-family: var(--font-tajawal), 'Tajawal', 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n  }\\n  \\n  /* RTL specific styles */\\n  [dir=\\\"rtl\\\"] {\\n    text-align: right;\\n  }\\n  \\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 8px;\\n  }\\n  \\n  ::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  ::-webkit-scrollbar-thumb {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-thumb {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  ::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n  \\n  :is(.dark *)::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.container {\\n  width: 100%;\\n}\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\n.glass {\\n  background: rgba(255, 255, 255, 0.1);\\n  backdrop-filter: blur(20px);\\n  -webkit-backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease-in-out;\\n}\\n.glass:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.glass-button {\\n  background: rgba(255, 255, 255, 0.1);\\n  backdrop-filter: blur(20px);\\n  -webkit-backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  padding: 12px 24px;\\n  transition: all 0.3s ease-in-out;\\n  cursor: pointer;\\n}\\n.glass-button:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.glass-card {\\n  background: rgba(255, 255, 255, 0.08);\\n  backdrop-filter: blur(24px);\\n  -webkit-backdrop-filter: blur(24px);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease-in-out;\\n}\\n.glass-card:hover {\\n  background: rgba(255, 255, 255, 0.12);\\n  transform: translateY(-4px);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n}\\n/* Enhanced Button variants with glass effects */\\n.btn-primary {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-primary:hover {\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  --tw-gradient-from: #0369a1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(3 105 161 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: #075985 var(--tw-gradient-to-position);\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-primary {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n.btn-secondary {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(255 255 255 / 0.2);\\n  background-color: rgb(255 255 255 / 0.1);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-secondary:hover {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-color: rgb(255 255 255 / 0.3);\\n  background-color: rgb(255 255 255 / 0.2);\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-secondary:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n.btn-outline {\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(209 213 219 / 0.3);\\n  background-color: rgb(255 255 255 / 0.05);\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-outline:hover {\\n  border-color: rgb(156 163 175 / 0.5);\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\n.btn-outline:is(.dark *) {\\n  border-color: rgb(75 85 99 / 0.3);\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:hover:is(.dark *) {\\n  border-color: rgb(107 114 128 / 0.5);\\n}\\n.btn-outline {\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n  }\\n/* Enhanced Glass effect buttons */\\n.btn-glass {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  border-radius: 0.75rem;\\n  background-color: rgb(255 255 255 / 0.1);\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.btn-glass:hover {\\n  --tw-translate-y: -0.25rem;\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\n.btn-glass {\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-glass:hover {\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n/* Enhanced glass button with premium effects */\\n/* Premium glass button for hero and main CTAs */\\n.glass-button {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1.5px solid rgba(255, 255, 255, 0.2);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    font-size: 1rem;\\n    font-weight: 600;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n.glass-button::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);\\n    transition: left 0.5s ease;\\n  }\\n.glass-button:hover::before {\\n    left: 100%;\\n  }\\n.glass-button:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.10) 100%);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n/* Glass card for hero stats and feature cards */\\n.glass-card {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 16px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.glass-card:hover {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    border-color: rgba(255, 255, 255, 0.25);\\n  }\\n/* Enhanced Card styles with glass effects */\\n.card-premium {\\n  border-radius: 1.5rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 500ms;\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(30px);\\n    backdrop-filter: blur(30px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n.card-premium:hover {\\n    transform: translateY(-8px) scale(1.03);\\n    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  }\\n/* Input styles */\\n/* Section spacing */\\n.section-padding {\\n  padding-top: 4rem;\\n  padding-bottom: 4rem;\\n}\\n@media (min-width: 1024px) {\\n\\n  .section-padding {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n}\\n.container-padding {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px) {\\n\\n  .container-padding {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container-padding {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n/* Enhanced Typography with premium styling - Dark theme optimized */\\n.heading-hero {\\n    font-size: clamp(3.5rem, 12vw, 8rem);\\n    font-weight: 900;\\n    line-height: 0.85;\\n    letter-spacing: -0.04em;\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\\n    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\\n  }\\n.heading-lg {\\n  font-family: var(--font-display), Poppins, system-ui, sans-serif;\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n  font-weight: 700;\\n  letter-spacing: -0.025em;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-lg {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n}\\n.heading-md {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n  font-weight: 600;\\n  letter-spacing: -0.025em;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-md {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n}\\n.heading-sm {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n  font-weight: 600;\\n}\\n@media (min-width: 1024px) {\\n\\n  .heading-sm {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\n/* Enhanced gradient text effects */\\n.gradient-text {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: rgb(217 70 239 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #d946ef var(--tw-gradient-via-position), var(--tw-gradient-to);\\n  --tw-gradient-to: #0369a1 var(--tw-gradient-to-position);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  color: transparent;\\n    background-size: 200% 200%;\\n    animation: gradientShift 3s ease-in-out infinite;\\n}\\n/* Premium Gold/Metallic Text Effects */\\n.gradient-text-gold {\\n    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #ffb300 75%, #ffd700 100%);\\n    background-size: 400% 400%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: goldShine 3s ease-in-out infinite;\\n    text-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.3));\\n  }\\n/* Sharp version without blur effects for hero title */\\n.gradient-text-gold-premium-sharp {\\n    background: linear-gradient(90deg,\\n      #8B6914 0%,\\n      #B8860B 10%,\\n      #DAA520 20%,\\n      #FFD700 30%,\\n      #FFED4E 40%,\\n      #FFF8DC 50%,\\n      #FFED4E 60%,\\n      #FFD700 70%,\\n      #DAA520 80%,\\n      #B8860B 90%,\\n      #8B6914 100%);\\n    background-size: 300% 100%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGoldShineSharp 4s ease-in-out infinite;\\n    /* Removed all blur-related properties */\\n    text-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));\\n    position: relative;\\n    /* Enhanced text rendering for sharpness */\\n    text-rendering: optimizeLegibility;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1;\\n    transform: translateZ(0);\\n    will-change: background-position;\\n    backface-visibility: hidden;\\n  }\\n/* Enhanced Arabic typography */\\n.text-arabic {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n.text-arabic-premium {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n    font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n    color: var(--text-primary);\\n    text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);\\n    letter-spacing: 0.025em;\\n    line-height: 1.3;\\n    font-weight: 700;\\n  }\\n/* Premium section headers with Syrian cultural elements */\\n/* Gold accent elements */\\n/* Premium glass morphism cards */\\n/* Gold gradient buttons */\\n/* Shimmer effect for buttons and cards */\\n/* Popular badge styling */\\n/* Tab navigation premium styling */\\n/* Premium Gold/Metallic Buttons */\\n.btn-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 15%,\\n      #DAA520 30%,\\n      #FFD700 45%,\\n      #FFED4E 60%,\\n      #FFD700 75%,\\n      #DAA520 90%,\\n      #B8860B 100%);\\n    background-size: 300% 300%;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 2px solid rgba(255, 215, 0, 0.4);\\n    box-shadow:\\n      0 8px 25px rgba(255, 215, 0, 0.4),\\n      0 4px 15px rgba(184, 134, 11, 0.3),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.3),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.2);\\n    color: #1a1a1a;\\n    font-weight: 700;\\n    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.4);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n    animation: goldShine 4s ease-in-out infinite;\\n  }\\n.btn-gold-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n.btn-gold-premium:hover::before {\\n    left: 100%;\\n  }\\n.btn-gold-premium:hover {\\n    transform: translateY(-3px) scale(1.05);\\n    box-shadow:\\n      0 15px 40px rgba(255, 215, 0, 0.5),\\n      0 8px 25px rgba(184, 134, 11, 0.4),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.4),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.3);\\n    background-position: 100% 100%;\\n    filter: brightness(1.1) saturate(1.1);\\n    border-color: rgba(255, 215, 0, 0.6);\\n  }\\n/* Metallic Card Effects */\\n.card-metallic-gold {\\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 215, 0, 0.2);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-metallic-gold::before {\\n    content: '';\\n    position: absolute;\\n    top: -50%;\\n    left: -50%;\\n    width: 200%;\\n    height: 200%;\\n    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);\\n    animation: goldGlow 4s ease-in-out infinite;\\n    pointer-events: none;\\n  }\\n.card-metallic-gold:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n    border-color: rgba(255, 215, 0, 0.4);\\n  }\\n/* Animation utilities */\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.visible {\\n  visibility: visible;\\n}\\n.static {\\n  position: static;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.-inset-4 {\\n  inset: -1rem;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-right-2 {\\n  right: -0.5rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.-top-2 {\\n  top: -0.5rem;\\n}\\n.-top-6 {\\n  top: -1.5rem;\\n}\\n.bottom-0 {\\n  bottom: 0px;\\n}\\n.bottom-28 {\\n  bottom: 7rem;\\n}\\n.bottom-32 {\\n  bottom: 8rem;\\n}\\n.bottom-36 {\\n  bottom: 9rem;\\n}\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\n.bottom-6 {\\n  bottom: 1.5rem;\\n}\\n.bottom-8 {\\n  bottom: 2rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\n.left-10 {\\n  left: 2.5rem;\\n}\\n.left-12 {\\n  left: 3rem;\\n}\\n.left-14 {\\n  left: 3.5rem;\\n}\\n.left-16 {\\n  left: 4rem;\\n}\\n.left-20 {\\n  left: 5rem;\\n}\\n.left-6 {\\n  left: 1.5rem;\\n}\\n.left-8 {\\n  left: 2rem;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-1 {\\n  right: 0.25rem;\\n}\\n.right-1\\\\/4 {\\n  right: 25%;\\n}\\n.right-10 {\\n  right: 2.5rem;\\n}\\n.right-12 {\\n  right: 3rem;\\n}\\n.right-16 {\\n  right: 4rem;\\n}\\n.right-20 {\\n  right: 5rem;\\n}\\n.right-4 {\\n  right: 1rem;\\n}\\n.right-6 {\\n  right: 1.5rem;\\n}\\n.right-full {\\n  right: 100%;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.top-1 {\\n  top: 0.25rem;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.top-20 {\\n  top: 5rem;\\n}\\n.top-24 {\\n  top: 6rem;\\n}\\n.top-28 {\\n  top: 7rem;\\n}\\n.top-32 {\\n  top: 8rem;\\n}\\n.top-4 {\\n  top: 1rem;\\n}\\n.top-40 {\\n  top: 10rem;\\n}\\n.top-6 {\\n  top: 1.5rem;\\n}\\n.isolate {\\n  isolation: isolate;\\n}\\n.-z-10 {\\n  z-index: -10;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-20 {\\n  z-index: 20;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.z-\\\\[9999\\\\] {\\n  z-index: 9999;\\n}\\n.-m-4 {\\n  margin: -1rem;\\n}\\n.-m-6 {\\n  margin: -1.5rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-20 {\\n  margin-top: 5rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-1 {\\n  height: 0.25rem;\\n}\\n.h-1\\\\.5 {\\n  height: 0.375rem;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-14 {\\n  height: 3.5rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-24 {\\n  height: 6rem;\\n}\\n.h-28 {\\n  height: 7rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-36 {\\n  height: 9rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-40 {\\n  height: 10rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-7 {\\n  height: 1.75rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.h-0\\\\.5 {\\n  height: 0.125rem;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-1 {\\n  width: 0.25rem;\\n}\\n.w-1\\\\.5 {\\n  width: 0.375rem;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-14 {\\n  width: 3.5rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-28 {\\n  width: 7rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-36 {\\n  width: 9rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-40 {\\n  width: 10rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-7 {\\n  width: 1.75rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\n.max-w-6xl {\\n  max-width: 72rem;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-x-full {\\n  --tw-translate-x: -100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-skew-x-12 {\\n  --tw-skew-x: -12deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-x-0 {\\n  --tw-scale-x: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n.resize-none {\\n  resize: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.items-baseline {\\n  align-items: baseline;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-12 {\\n  gap: 3rem;\\n}\\n.gap-16 {\\n  gap: 4rem;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.rounded-3xl {\\n  border-radius: 1.5rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.bg-gold-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gold-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(253 244 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-transparent {\\n  background-color: transparent;\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n.bg-white\\\\/95 {\\n  background-color: rgb(255 255 255 / 0.95);\\n}\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-gold-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-50 {\\n  --tw-gradient-from: #f0f9ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(240 249 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary-500 {\\n  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500\\\\/20 {\\n  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-transparent {\\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-500\\\\/20 {\\n  --tw-gradient-from: rgb(234 179 8 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-gold-600 {\\n  --tw-gradient-to: rgb(217 119 6 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #d97706 var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.via-white\\\\/20 {\\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-blue-500\\\\/20 {\\n  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-orange-500\\\\/20 {\\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\\n}\\n.to-primary-500 {\\n  --tw-gradient-to: #0ea5e9 var(--tw-gradient-to-position);\\n}\\n.to-secondary-50 {\\n  --tw-gradient-to: #fdf4ff var(--tw-gradient-to-position);\\n}\\n.to-secondary-500 {\\n  --tw-gradient-to: #d946ef var(--tw-gradient-to-position);\\n}\\n.to-transparent {\\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-10 {\\n  padding-left: 2.5rem;\\n  padding-right: 2.5rem;\\n}\\n.px-12 {\\n  padding-left: 3rem;\\n  padding-right: 3rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.text-start {\\n  text-align: start;\\n}\\n.font-cairo {\\n  font-family: var(--font-cairo), Cairo, Tajawal, Noto Sans Arabic, system-ui, sans-serif;\\n}\\n.font-display {\\n  font-family: var(--font-display), Poppins, system-ui, sans-serif;\\n}\\n.font-sans {\\n  font-family: var(--font-inter), Inter, system-ui, sans-serif;\\n}\\n.font-tajawal {\\n  font-family: var(--font-tajawal), Tajawal, Cairo, Noto Sans Arabic, system-ui, sans-serif;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-5xl {\\n  font-size: 3rem;\\n  line-height: 1;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-black {\\n  font-weight: 900;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-success-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-white\\\\/40 {\\n  color: rgb(255 255 255 / 0.4);\\n}\\n.text-white\\\\/50 {\\n  color: rgb(255 255 255 / 0.5);\\n}\\n.text-white\\\\/60 {\\n  color: rgb(255 255 255 / 0.6);\\n}\\n.text-white\\\\/70 {\\n  color: rgb(255 255 255 / 0.7);\\n}\\n.text-white\\\\/80 {\\n  color: rgb(255 255 255 / 0.8);\\n}\\n.text-white\\\\/90 {\\n  color: rgb(255 255 255 / 0.9);\\n}\\n.text-white\\\\/95 {\\n  color: rgb(255 255 255 / 0.95);\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.underline {\\n  text-decoration-line: underline;\\n}\\n.decoration-white\\\\/30 {\\n  text-decoration-color: rgb(255 255 255 / 0.3);\\n}\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\n.antialiased {\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-10 {\\n  opacity: 0.1;\\n}\\n.opacity-15 {\\n  opacity: 0.15;\\n}\\n.opacity-20 {\\n  opacity: 0.2;\\n}\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\n.opacity-30 {\\n  opacity: 0.3;\\n}\\n.opacity-40 {\\n  opacity: 0.4;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.ring-2 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-purple-400 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));\\n}\\n.ring-yellow-400 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(250 204 21 / var(--tw-ring-opacity, 1));\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow {\\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.drop-shadow-lg {\\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.grayscale {\\n  --tw-grayscale: grayscale(100%);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-1000 {\\n  transition-duration: 1000ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.rtl {\\n  direction: rtl;\\n}\\n.ltr {\\n  direction: ltr;\\n}\\n/* Text direction utilities */\\n.text-start {\\n    text-align: start;\\n  }\\n/* Margin utilities for RTL */\\n/* Padding utilities for RTL */\\n\\n/* Import theme utilities */\\n\\n/* CSS Variables for theming - Enhanced theme system */\\n:root {\\n  /* Legacy theme variables (maintained for compatibility) */\\n  --primary-bg: #0f172a;\\n  --secondary-bg: #1e293b;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --glass-bg: rgba(255, 255, 255, 0.1);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --toast-bg: #1f2937;\\n  --toast-color: #f9fafb;\\n\\n  /* New theme system variables (will be overridden by theme switching) */\\n  --theme-primary-500: #f59e0b;\\n  --theme-glass-bg: rgba(255, 215, 0, 0.12);\\n  --theme-glass-border: rgba(255, 215, 0, 0.25);\\n  --theme-glass-shadow: 0 8px 32px rgba(255, 215, 0, 0.15);\\n  --theme-glass-blur: blur(25px);\\n  --theme-text-primary: #ffffff;\\n  --theme-text-secondary: rgba(255, 255, 255, 0.8);\\n  --theme-text-accent: #FFD700;\\n  --theme-text-muted: rgba(255, 255, 255, 0.6);\\n  --theme-gradient-text: linear-gradient(90deg, #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%, #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%, #DAA520 80%, #B8860B 90%, #8B6914 100%);\\n  --theme-gradient-button: linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%);\\n  --theme-shadow-premium: 0 25px 50px rgba(255, 215, 0, 0.3);\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --primary-bg: #ffffff;\\n  --secondary-bg: #f8fafc;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #1f2937;\\n  --text-secondary: #64748b;\\n  --glass-bg: rgba(255, 255, 255, 0.8);\\n  --glass-border: rgba(0, 0, 0, 0.1);\\n  --toast-bg: #ffffff;\\n  --toast-color: #1f2937;\\n}\\n\\n/* Base styles */\\n\\n/* Component styles */\\n\\n/* Utility classes */\\n\\n/* Keyframe animations */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes fadeInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Enhanced keyframe animations for glass effects */\\n@keyframes gradientShift {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGradient {\\n  0%, 100% { background-position: 0% 0%; }\\n  25% { background-position: 100% 0%; }\\n  50% { background-position: 100% 100%; }\\n  75% { background-position: 0% 100%; }\\n}\\n\\n@keyframes glassShimmer {\\n  0% { background-position: -200% 0; }\\n  100% { background-position: 200% 0; }\\n}\\n\\n@keyframes floatGlass {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n    /* Removed blur filter */\\n  }\\n  25% {\\n    transform: translateY(-10px) rotate(1deg);\\n    /* Removed blur filter */\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(0deg);\\n    /* Removed blur filter */\\n  }\\n  75% {\\n    transform: translateY(-10px) rotate(-1deg);\\n    /* Removed blur filter */\\n  }\\n}\\n\\n@keyframes pulseGlass {\\n  0%, 100% {\\n    background: rgba(255, 255, 255, 0.1);\\n    border-color: rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n  50% {\\n    background: rgba(255, 255, 255, 0.15);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n/* Premium Gold/Metallic Animations */\\n@keyframes goldShine {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGoldShine {\\n  0% { background-position: -200% 0%; }\\n  50% { background-position: 0% 0%; }\\n  100% { background-position: 200% 0%; }\\n}\\n\\n/* Sharp animation without any blur effects */\\n@keyframes premiumGoldShineSharp {\\n  0% {\\n    background-position: -200% 0%;\\n    transform: translateZ(0);\\n  }\\n  50% {\\n    background-position: 0% 0%;\\n    transform: translateZ(0);\\n  }\\n  100% {\\n    background-position: 200% 0%;\\n    transform: translateZ(0);\\n  }\\n}\\n\\n@keyframes goldGlimmer {\\n  0% { transform: translateX(-100%) skewX(-15deg); }\\n  100% { transform: translateX(200%) skewX(-15deg); }\\n}\\n\\n@keyframes textShimmer {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n\\n\\n@keyframes goldGlow {\\n  0%, 100% {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: rotate(180deg) scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n\\n/* Reduced motion preferences */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n\\n  .glass,\\n  .glass-card,\\n  .glass-button {\\n    background: white !important;\\n    backdrop-filter: none !important;\\n    -webkit-backdrop-filter: none !important;\\n    border: 1px solid #ccc !important;\\n  }\\n}\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\n.hover\\\\:text-primary-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-white\\\\/50:focus {\\n  --tw-ring-color: rgb(255 255 255 / 0.5);\\n}\\n.group:hover .group-hover\\\\:translate-x-full {\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:rotate-12 {\\n  --tw-rotate: 12deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-110 {\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.group:hover .group-hover\\\\:opacity-30 {\\n  opacity: 0.3;\\n}\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:border-gray-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:bg-gray-600:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:bg-gray-900:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:bg-gray-900\\\\/95:is(.dark *) {\\n  background-color: rgb(17 24 39 / 0.95);\\n}\\n.dark\\\\:bg-primary-900\\\\/20:is(.dark *) {\\n  background-color: rgb(12 74 110 / 0.2);\\n}\\n.dark\\\\:bg-secondary-900\\\\/20:is(.dark *) {\\n  background-color: rgb(112 26 117 / 0.2);\\n}\\n.dark\\\\:from-gray-800:is(.dark *) {\\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.dark\\\\:to-gray-700:is(.dark *) {\\n  --tw-gradient-to: #374151 var(--tw-gradient-to-position);\\n}\\n.dark\\\\:text-gray-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-gray-400:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-gray-600:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.dark\\\\:hover\\\\:bg-gray-800:hover:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n.dark\\\\:hover\\\\:text-primary-400:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:h-10 {\\n    height: 2.5rem;\\n  }\\n\\n  .lg\\\\:h-20 {\\n    height: 5rem;\\n  }\\n\\n  .lg\\\\:h-6 {\\n    height: 1.5rem;\\n  }\\n\\n  .lg\\\\:w-10 {\\n    width: 2.5rem;\\n  }\\n\\n  .lg\\\\:w-6 {\\n    width: 1.5rem;\\n  }\\n\\n  .lg\\\\:scale-110 {\\n    --tw-scale-x: 1.1;\\n    --tw-scale-y: 1.1;\\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:p-12 {\\n    padding: 3rem;\\n  }\\n\\n  .lg\\\\:p-16 {\\n    padding: 4rem;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .lg\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .lg\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .lg\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .lg\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n.rtl\\\\:space-x-reverse:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4DAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,sCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;KAAd,sBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,uBAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd,yDAAc;EAAd,oBAAc;EAAd,mDAAc;EAAd,+FAAc;EAAd,wDAAc;EAAd,0BAAc;IAAd,6BAAc;IAAd;AAAc;;EAAd,sCAAc;EAAd;IAAA,uEAAc;IAAd,mDAAc;IAAd,kCAAc;EAAA;;EAAd;IAAA,6FAAc;IAAd,6DAAc;EAAA;;EAAd;IAAA,+FAAc;IAAd,mDAAc;EAAA;;EAAd,wBAAc;EAAd;IAAA,iBAAc;EAAA;;EAAd,qBAAc;EAAd;IAAA,UAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,qBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;EAAA,oCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,yCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,oCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,mBAAoB;EAApB,kBAAoB;EAApB,gCAAoB;EAApB;AAAoB;AAApB;EAAA,oCAAoB;EAApB,uCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB,mCAAoB;EAApB,2CAAoB;EAApB,mBAAoB;EAApB,aAAoB;EAApB,yCAAoB;EAApB;AAAoB;AAApB;EAAA,qCAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAyGlB,gDAAgD;AAE9C;EAAA,+LAAsP;EAAtP,sBAAsP;EAAtP,qEAAsP;EAAtP,4DAAsP;EAAtP,mEAAsP;EAAtP,mEAAsP;EAAtP,wDAAsP;EAAtP,kBAAsP;EAAtP,mBAAsP;EAAtP,iBAAsP;EAAtP,oBAAsP;EAAtP,gBAAsP;EAAtP,oBAAsP;EAAtP,mDAAsP;EAAtP,+EAAsP;EAAtP,mGAAsP;EAAtP,uGAAsP;EAAtP,wBAAsP;EAAtP,wDAAsP;EAAtP;AAAsP;AAAtP;EAAA,0BAAsP;EAAtP,kBAAsP;EAAtP,kBAAsP;EAAtP,+LAAsP;EAAtP,4DAAsP;EAAtP,mEAAsP;EAAtP,mEAAsP;EAAtP,wDAAsP;EAAtP,gFAAsP;EAAtP,oGAAsP;EAAtP;AAAsP;AADxP;IAEE,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;EAC5C;AAGE;EAAA,+LAAgP;EAAhP,sBAAgP;EAAhP,iBAAgP;EAAhP,oCAAgP;EAAhP,wCAAgP;EAAhP,kBAAgP;EAAhP,mBAAgP;EAAhP,iBAAgP;EAAhP,oBAAgP;EAAhP,gBAAgP;EAAhP,oBAAgP;EAAhP,iDAAgP;EAAhP,6EAAgP;EAAhP,iGAAgP;EAAhP,uGAAgP;EAAhP,wBAAgP;EAAhP,wDAAgP;EAAhP;AAAgP;AAAhP;EAAA,0BAAgP;EAAhP,+LAAgP;EAAhP,oCAAgP;EAAhP,wCAAgP;EAAhP,+EAAgP;EAAhP,mGAAgP;EAAhP;AAAgP;AAAhP;EAAA,oBAAgP;EAAhP;AAAgP;AADlP;IAEE,mCAAmC;IACnC,2BAA2B;EAC7B;AAGE;EAAA,sBAA0O;EAA1O,iBAA0O;EAA1O,oCAA0O;EAA1O,yCAA0O;EAA1O,oBAA0O;EAA1O,qBAA0O;EAA1O,oBAA0O;EAA1O,uBAA0O;EAA1O,gBAA0O;EAA1O,oBAA0O;EAA1O,gDAA0O;EAA1O,wBAA0O;EAA1O,wDAA0O;EAA1O;AAA0O;AAA1O;EAAA,oCAA0O;EAA1O;AAA0O;AAA1O;EAAA,iCAA0O;EAA1O,oBAA0O;EAA1O;AAA0O;AAA1O;EAAA;AAA0O;AAD5O;IAEE,mCAAmC;IACnC,2BAA2B;EAC7B;AAEA,kCAAkC;AAEhC;EAAA,+LAA6J;EAA7J,sBAA6J;EAA7J,wCAA6J;EAA7J,kBAA6J;EAA7J,mBAA6J;EAA7J,iBAA6J;EAA7J,oBAA6J;EAA7J,gBAA6J;EAA7J,oBAA6J;EAA7J,mDAA6J;EAA7J,wBAA6J;EAA7J,wDAA6J;EAA7J;AAA6J;AAA7J;EAAA,0BAA6J;EAA7J,kBAA6J;EAA7J,kBAA6J;EAA7J,+LAA6J;EAA7J;AAA6J;AAD/J;IAEE,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;IAC1C,yCAAyC;EAC3C;AAEA;IACE,2CAA2C;EAC7C;AAEA,+CAA+C;AAqC/C,gDAAgD;AAChD;IACE,iGAAiG;IACjG,mCAAmC;IACnC,2BAA2B;IAC3B,4CAA4C;IAC5C,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,iDAAiD;IACjD,kBAAkB;IAClB,gBAAgB;IAChB,yCAAyC;EAC3C;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,mGAAmG;IACnG,0BAA0B;EAC5B;AAEA;IACE,UAAU;EACZ;AAEA;IACE,uCAAuC;IACvC,iGAAiG;IACjG,sCAAsC;IACtC,qFAAqF;EACvF;AAEA,gDAAgD;AAChD;IACE,iGAAiG;IACjG,mCAAmC;IACnC,2BAA2B;IAC3B,2CAA2C;IAC3C,mBAAmB;IACnB,yCAAyC;IACzC,iDAAiD;EACnD;AAEA;IACE,iGAAiG;IACjG,uCAAuC;IACvC,2CAA2C;IAC3C,uCAAuC;EACzC;AAEA,4CAA4C;AA2B1C;EAAA,qBAA8C;EAA9C,wBAA8C;EAA9C,wDAA8C;EAA9C,0BAA8C;IAC9C,gGAAgG;IAChG,mCAAmC;IACnC,2BAA2B;IAC3B,0CAA0C;IAC1C;AAL8C;AAQhD;IACE,uCAAuC;IACvC,mFAAmF;EACrF;AAEA,iBAAiB;AAKjB,oBAAoB;AAElB;EAAA,iBAAqB;EAArB;AAAqB;AAArB;;EAAA;IAAA,iBAAqB;IAArB;EAAqB;AAAA;AAIrB;EAAA,kBAA2B;EAA3B;AAA2B;AAA3B;;EAAA;IAAA,oBAA2B;IAA3B;EAA2B;AAAA;AAA3B;;EAAA;IAAA,kBAA2B;IAA3B;EAA2B;AAAA;AAG7B,oEAAoE;AACpE;IACE,oCAAoC;IACpC,gBAAgB;IAChB,iBAAiB;IACjB,uBAAuB;IACvB,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,0EAA0E;IAC1E,qBAAqB;IACrB,6BAA6B;IAC7B,oCAAoC;IACpC,0CAA0C;IAC1C,iDAAiD;EACnD;AAeE;EAAA,gEAAiE;EAAjE,mBAAiE;EAAjE,oBAAiE;EAAjE,gBAAiE;EAAjE;AAAiE;AAAjE;;EAAA;IAAA,kBAAiE;IAAjE;EAAiE;AAAA;AAIjE;EAAA,iBAAwD;EAAxD,iBAAwD;EAAxD,gBAAwD;EAAxD;AAAwD;AAAxD;;EAAA;IAAA,mBAAwD;IAAxD;EAAwD;AAAA;AAIxD;EAAA,kBAAwC;EAAxC,oBAAwC;EAAxC;AAAwC;AAAxC;;EAAA;IAAA,iBAAwC;IAAxC;EAAwC;AAAA;AAG1C,mCAAmC;AAEjC;EAAA,qEAAuG;EAAvG,4DAAuG;EAAvG,mEAAuG;EAAvG,mEAAuG;EAAvG,qEAAuG;EAAvG,4GAAuG;EAAvG,wDAAuG;EAAvG,6BAAuG;UAAvG,qBAAuG;EAAvG,kBAAuG;IACvG,0BAA0B;IAC1B;AAFuG;AAezG,uCAAuC;AACvC;IACE,oGAAoG;IACpG,0BAA0B;IAC1B,6BAA6B;IAC7B,qBAAqB;IACrB,oCAAoC;IACpC,4CAA4C;IAC5C,8CAA8C;IAC9C,qDAAqD;EACvD;AAyBA,sDAAsD;AACtD;IACE;;;;;;;;;;;mBAWe;IACf,0BAA0B;IAC1B,6BAA6B;IAC7B,qBAAqB;IACrB,oCAAoC;IACpC,wDAAwD;IACxD,wCAAwC;IACxC,6CAA6C;IAC7C,qDAAqD;IACrD,kBAAkB;IAClB,0CAA0C;IAC1C,kCAAkC;IAClC,mCAAmC;IACnC,kCAAkC;IAClC,yCAAyC;IACzC,wBAAwB;IACxB,gCAAgC;IAChC,2BAA2B;EAC7B;AASA,+BAA+B;AAE7B;IAAA,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,uEAAuE;IACvE,mDAAmD;IACnD,kCAAkC;EAHjB;AAajB;IAAA,6FAAiB;IAAjB,6DAAiB;IAAjB,uFAAiB;IACjB,uEAAuE;IACvE,mDAAmD;IACnD,kCAAkC;IAClC,0BAA0B;IAC1B,6CAA6C;IAC7C,uBAAuB;IACvB,gBAAgB;IAChB,gBAAgB;EARC;AAWnB,0DAA0D;AAU1D,yBAAyB;AAOzB,iCAAiC;AAiBjC,0BAA0B;AAmB1B,yCAAyC;AAMzC,0BAA0B;AAQ1B,mCAAmC;AAUnC,kCAAkC;AAClC;IACE;;;;;;;;mBAQe;IACf,0BAA0B;IAC1B,mCAAmC;IACnC,2BAA2B;IAC3B,wCAAwC;IACxC;;;;4CAIwC;IACxC,cAAc;IACd,gBAAgB;IAChB,+CAA+C;IAC/C,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,kBAAkB;IAClB,gBAAgB;IAChB,4CAA4C;EAC9C;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,kGAAkG;IAClG,0BAA0B;EAC5B;AAEA;IACE,UAAU;EACZ;AAEA;IACE,uCAAuC;IACvC;;;;4CAIwC;IACxC,8BAA8B;IAC9B,qCAAqC;IACrC,oCAAoC;EACtC;AAEA,0BAA0B;AAC1B;IACE,4FAA4F;IAC5F,mCAAmC;IACnC,2BAA2B;IAC3B,wCAAwC;IACxC,mBAAmB;IACnB,qFAAqF;IACrF,gCAAgC;IAChC,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,+EAA+E;IAC/E,2CAA2C;IAC3C,oBAAoB;EACtB;AAEA;IACE,uCAAuC;IACvC,sFAAsF;IACtF,oCAAoC;EACtC;AAEA,wBAAwB;AA/lB1B;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,gEAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mCAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kGAAmB;EAAnB;AAAmB;AAAnB;EAAA,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAmnBjB,6BAA6B;AAC7B;IACE,iBAAiB;EACnB;AAMA,6BAA6B;AAS7B,8BAA8B;;AAnoBhC,2BAA2B;;AAG3B,sDAAsD;AACtD;EACE,0DAA0D;EAC1D,qBAAqB;EACrB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,yBAAyB;EACzB,oCAAoC;EACpC,wCAAwC;EACxC,mBAAmB;EACnB,sBAAsB;;EAEtB,uEAAuE;EACvE,4BAA4B;EAC5B,yCAAyC;EACzC,6CAA6C;EAC7C,wDAAwD;EACxD,8BAA8B;EAC9B,6BAA6B;EAC7B,gDAAgD;EAChD,4BAA4B;EAC5B,4CAA4C;EAC5C,4LAA4L;EAC5L,uFAAuF;EACvF,0DAA0D;AAC5D;;AAEA;EACE,qBAAqB;EACrB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,yBAAyB;EACzB,oCAAoC;EACpC,kCAAkC;EAClC,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA,gBAAgB;;AAyDhB,qBAAqB;;AA2gBrB,oBAAoB;;AA8BpB,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,4BAA4B;EAC9B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,4BAA4B;EAC9B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA,mDAAmD;AACnD;EACE,WAAW,2BAA2B,EAAE;EACxC,MAAM,6BAA6B,EAAE;AACvC;;AAEA;EACE,WAAW,0BAA0B,EAAE;EACvC,MAAM,4BAA4B,EAAE;EACpC,MAAM,8BAA8B,EAAE;EACtC,MAAM,4BAA4B,EAAE;AACtC;;AAEA;EACE,KAAK,4BAA4B,EAAE;EACnC,OAAO,2BAA2B,EAAE;AACtC;;AAEA;EACE;IACE,uCAAuC;IACvC,wBAAwB;EAC1B;EACA;IACE,yCAAyC;IACzC,wBAAwB;EAC1B;EACA;IACE,yCAAyC;IACzC,wBAAwB;EAC1B;EACA;IACE,0CAA0C;IAC1C,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,oCAAoC;IACpC,sCAAsC;IACtC,yCAAyC;EAC3C;EACA;IACE,qCAAqC;IACrC,sCAAsC;IACtC,2CAA2C;EAC7C;AACF;;AAEA,qCAAqC;AACrC;EACE,WAAW,2BAA2B,EAAE;EACxC,MAAM,6BAA6B,EAAE;AACvC;;AAEA;EACE,KAAK,6BAA6B,EAAE;EACpC,MAAM,0BAA0B,EAAE;EAClC,OAAO,4BAA4B,EAAE;AACvC;;AAEA,6CAA6C;AAC7C;EACE;IACE,6BAA6B;IAC7B,wBAAwB;EAC1B;EACA;IACE,0BAA0B;IAC1B,wBAAwB;EAC1B;EACA;IACE,4BAA4B;IAC5B,wBAAwB;EAC1B;AACF;;AAEA;EACE,KAAK,0CAA0C,EAAE;EACjD,OAAO,yCAAyC,EAAE;AACpD;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,yBAAyB;IACzB,UAAU;EACZ;EACA;IACE,2BAA2B;IAC3B,UAAU;EACZ;AACF;;;;AAIA;EACE;IACE,gCAAgC;IAChC,YAAY;EACd;EACA;IACE,oCAAoC;IACpC,YAAY;EACd;AACF;;AAEA,+BAA+B;AAC/B;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAEA;;;IAGE,4BAA4B;IAC5B,gCAAgC;IAChC,wCAAwC;IACxC,iCAAiC;EACnC;AACF;AAv0BA;EAAA,kBAw0BA;EAx0BA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,8BAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,2GAw0BA;EAx0BA,yGAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,sBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,iBAw0BA;EAx0BA,iBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,eAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,sBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,sBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,4DAw0BA;EAx0BA,kEAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,kBAw0BA;EAx0BA;AAw0BA;AAx0BA;EAAA,oBAw0BA;EAx0BA;AAw0BA;AAx0BA;;EAAA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA,oBAw0BA;IAx0BA;EAw0BA;AAAA;AAx0BA;;EAAA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;AAAA;AAx0BA;;EAAA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA,iBAw0BA;IAx0BA,iBAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA;EAw0BA;;EAx0BA;IAAA,kBAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA,iBAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA,iBAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA,mBAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA,eAw0BA;IAx0BA;EAw0BA;;EAx0BA;IAAA,kBAw0BA;IAx0BA;EAw0BA;AAAA;AAx0BA;;EAAA;IAAA,kBAw0BA;IAx0BA;EAw0BA;AAAA;AAx0BA;EAAA;AAw0BA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Import theme utilities */\\n@import './themes/theme-base.css';\\n\\n/* CSS Variables for theming - Enhanced theme system */\\n:root {\\n  /* Legacy theme variables (maintained for compatibility) */\\n  --primary-bg: #0f172a;\\n  --secondary-bg: #1e293b;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #f8fafc;\\n  --text-secondary: #cbd5e1;\\n  --glass-bg: rgba(255, 255, 255, 0.1);\\n  --glass-border: rgba(255, 255, 255, 0.2);\\n  --toast-bg: #1f2937;\\n  --toast-color: #f9fafb;\\n\\n  /* New theme system variables (will be overridden by theme switching) */\\n  --theme-primary-500: #f59e0b;\\n  --theme-glass-bg: rgba(255, 215, 0, 0.12);\\n  --theme-glass-border: rgba(255, 215, 0, 0.25);\\n  --theme-glass-shadow: 0 8px 32px rgba(255, 215, 0, 0.15);\\n  --theme-glass-blur: blur(25px);\\n  --theme-text-primary: #ffffff;\\n  --theme-text-secondary: rgba(255, 255, 255, 0.8);\\n  --theme-text-accent: #FFD700;\\n  --theme-text-muted: rgba(255, 255, 255, 0.6);\\n  --theme-gradient-text: linear-gradient(90deg, #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%, #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%, #DAA520 80%, #B8860B 90%, #8B6914 100%);\\n  --theme-gradient-button: linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%);\\n  --theme-shadow-premium: 0 25px 50px rgba(255, 215, 0, 0.3);\\n}\\n\\n[data-theme=\\\"light\\\"] {\\n  --primary-bg: #ffffff;\\n  --secondary-bg: #f8fafc;\\n  --accent-color: #0ea5e9;\\n  --text-primary: #1f2937;\\n  --text-secondary: #64748b;\\n  --glass-bg: rgba(255, 255, 255, 0.8);\\n  --glass-border: rgba(0, 0, 0, 0.1);\\n  --toast-bg: #ffffff;\\n  --toast-color: #1f2937;\\n}\\n\\n/* Base styles */\\n@layer base {\\n  html {\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    @apply bg-dark-900 text-gray-100 transition-colors duration-300;\\n    background: var(--primary-bg);\\n    color: var(--text-primary);\\n  }\\n  \\n  /* Enhanced Arabic font optimization */\\n  .font-arabic {\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .font-cairo {\\n    font-family: var(--font-cairo), 'Cairo', 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1;\\n  }\\n\\n  .font-tajawal {\\n    font-family: var(--font-tajawal), 'Tajawal', 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n  }\\n  \\n  /* RTL specific styles */\\n  [dir=\\\"rtl\\\"] {\\n    text-align: right;\\n  }\\n  \\n  [dir=\\\"rtl\\\"] .ltr-content {\\n    direction: ltr;\\n    text-align: left;\\n  }\\n  \\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 8px;\\n  }\\n  \\n  ::-webkit-scrollbar-track {\\n    @apply bg-gray-100 dark:bg-gray-800;\\n  }\\n  \\n  ::-webkit-scrollbar-thumb {\\n    @apply bg-gray-300 dark:bg-gray-600 rounded-full;\\n  }\\n  \\n  ::-webkit-scrollbar-thumb:hover {\\n    @apply bg-gray-400 dark:bg-gray-500;\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n  /* Enhanced Button variants with glass effects */\\n  .btn-primary {\\n    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-white/10 hover:bg-white/20 text-primary-600 dark:text-primary-400 border border-white/20 hover:border-white/30 font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n\\n  .btn-outline {\\n    @apply border border-gray-300/30 hover:border-gray-400/50 text-gray-700 dark:text-gray-300 dark:border-gray-600/30 dark:hover:border-gray-500/50 font-medium px-6 py-3 rounded-xl transition-all duration-300 bg-white/5 hover:bg-white/10;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n  }\\n\\n  /* Enhanced Glass effect buttons */\\n  .btn-glass {\\n    @apply bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-glass:hover {\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n\\n  /* Enhanced glass button with premium effects */\\n  .glass-button-enhanced {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);\\n    -webkit-backdrop-filter: blur(32px);\\n    backdrop-filter: blur(32px);\\n    border: 2px solid rgba(255, 255, 255, 0.25);\\n    border-radius: 16px;\\n    padding: 1.25rem 2.5rem;\\n    font-size: 1.125rem;\\n    font-weight: 600;\\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .glass-button-enhanced::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n\\n  .glass-button-enhanced:hover::before {\\n    left: 100%;\\n  }\\n\\n  .glass-button-enhanced:hover {\\n    transform: translateY(-3px) scale(1.03);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);\\n    border-color: rgba(255, 255, 255, 0.4);\\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n\\n  /* Premium glass button for hero and main CTAs */\\n  .glass-button {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1.5px solid rgba(255, 255, 255, 0.2);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    font-size: 1rem;\\n    font-weight: 600;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n    overflow: hidden;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .glass-button::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);\\n    transition: left 0.5s ease;\\n  }\\n\\n  .glass-button:hover::before {\\n    left: 100%;\\n  }\\n\\n  .glass-button:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.10) 100%);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;\\n  }\\n\\n  /* Glass card for hero stats and feature cards */\\n  .glass-card {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 16px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .glass-card:hover {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    border-color: rgba(255, 255, 255, 0.25);\\n  }\\n  \\n  /* Enhanced Card styles with glass effects */\\n  .card {\\n    @apply bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg border border-white/20 dark:border-gray-700/30 transition-all duration-300;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n  }\\n\\n  .card-hover {\\n    @apply hover:shadow-xl hover:-translate-y-2 transform transition-all duration-300 hover:scale-105;\\n  }\\n\\n  .card-glass {\\n    @apply rounded-2xl transition-all duration-300;\\n    background: rgba(255, 255, 255, 0.08);\\n    -webkit-backdrop-filter: blur(24px);\\n    backdrop-filter: blur(24px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .card-glass:hover {\\n    background: rgba(255, 255, 255, 0.12);\\n    transform: translateY(-4px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  }\\n\\n  .card-premium {\\n    @apply rounded-3xl transition-all duration-500;\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(30px);\\n    backdrop-filter: blur(30px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .card-premium:hover {\\n    transform: translateY(-8px) scale(1.03);\\n    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n  }\\n  \\n  /* Input styles */\\n  .input-field {\\n    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-all duration-200;\\n  }\\n  \\n  /* Section spacing */\\n  .section-padding {\\n    @apply py-16 lg:py-24;\\n  }\\n  \\n  .container-padding {\\n    @apply px-4 sm:px-6 lg:px-8;\\n  }\\n  \\n  /* Enhanced Typography with premium styling - Dark theme optimized */\\n  .heading-hero {\\n    font-size: clamp(3.5rem, 12vw, 8rem);\\n    font-weight: 900;\\n    line-height: 0.85;\\n    letter-spacing: -0.04em;\\n    @apply font-cairo;\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);\\n    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\\n  }\\n\\n  .heading-hero-mobile {\\n    font-size: clamp(2.5rem, 15vw, 4rem);\\n    font-weight: 800;\\n    line-height: 0.9;\\n    letter-spacing: -0.03em;\\n    @apply font-cairo;\\n  }\\n\\n  .heading-xl {\\n    @apply text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight font-display;\\n  }\\n\\n  .heading-lg {\\n    @apply text-3xl lg:text-4xl font-bold tracking-tight font-display;\\n  }\\n\\n  .heading-md {\\n    @apply text-2xl lg:text-3xl font-semibold tracking-tight;\\n  }\\n\\n  .heading-sm {\\n    @apply text-xl lg:text-2xl font-semibold;\\n  }\\n\\n  /* Enhanced gradient text effects */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-primary-600 via-secondary-500 to-primary-700 bg-clip-text text-transparent;\\n    background-size: 200% 200%;\\n    animation: gradientShift 3s ease-in-out infinite;\\n  }\\n\\n  .gradient-text-premium {\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);\\n    background-size: 300% 300%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGradient 4s ease-in-out infinite;\\n    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n  }\\n\\n  /* Premium Gold/Metallic Text Effects */\\n  .gradient-text-gold {\\n    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #ffb300 75%, #ffd700 100%);\\n    background-size: 400% 400%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: goldShine 3s ease-in-out infinite;\\n    text-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.3));\\n  }\\n\\n  .gradient-text-gold-premium {\\n    background: linear-gradient(90deg,\\n      #8B6914 0%,\\n      #B8860B 10%,\\n      #DAA520 20%,\\n      #FFD700 30%,\\n      #FFED4E 40%,\\n      #FFF8DC 50%,\\n      #FFED4E 60%,\\n      #FFD700 70%,\\n      #DAA520 80%,\\n      #B8860B 90%,\\n      #8B6914 100%);\\n    background-size: 300% 100%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGoldShine 4s ease-in-out infinite;\\n    text-shadow: 0 8px 20px rgba(255, 215, 0, 0.6);\\n    filter: drop-shadow(0 6px 16px rgba(255, 215, 0, 0.5)) drop-shadow(0 0 30px rgba(255, 215, 0, 0.3)) drop-shadow(0 2px 8px rgba(184, 134, 11, 0.4));\\n    position: relative;\\n  }\\n\\n  /* Sharp version without blur effects for hero title */\\n  .gradient-text-gold-premium-sharp {\\n    background: linear-gradient(90deg,\\n      #8B6914 0%,\\n      #B8860B 10%,\\n      #DAA520 20%,\\n      #FFD700 30%,\\n      #FFED4E 40%,\\n      #FFF8DC 50%,\\n      #FFED4E 60%,\\n      #FFD700 70%,\\n      #DAA520 80%,\\n      #B8860B 90%,\\n      #8B6914 100%);\\n    background-size: 300% 100%;\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    animation: premiumGoldShineSharp 4s ease-in-out infinite;\\n    /* Removed all blur-related properties */\\n    text-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);\\n    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));\\n    position: relative;\\n    /* Enhanced text rendering for sharpness */\\n    text-rendering: optimizeLegibility;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1;\\n    transform: translateZ(0);\\n    will-change: background-position;\\n    backface-visibility: hidden;\\n  }\\n\\n\\n\\n  .text-glass {\\n    color: rgba(255, 255, 255, 0.9);\\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  /* Enhanced Arabic typography */\\n  .text-arabic {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n  }\\n\\n  .text-arabic-display {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1;\\n    font-weight: 700;\\n  }\\n\\n  .text-arabic-premium {\\n    @apply font-cairo;\\n    font-feature-settings: \\\"kern\\\" 1, \\\"liga\\\" 1, \\\"calt\\\" 1, \\\"ss01\\\" 1, \\\"ss02\\\" 1;\\n    font-variant-ligatures: common-ligatures contextual;\\n    text-rendering: optimizeLegibility;\\n    color: var(--text-primary);\\n    text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);\\n    letter-spacing: 0.025em;\\n    line-height: 1.3;\\n    font-weight: 700;\\n  }\\n\\n  /* Premium section headers with Syrian cultural elements */\\n  .section-header-premium {\\n    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 60%, #cbd5e1 100%);\\n    -webkit-background-clip: text;\\n    background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\\n  }\\n\\n  /* Gold accent elements */\\n  .gold-accent-vertical {\\n    background: linear-gradient(180deg, #FFD700 0%, #B8860B 100%);\\n    border-radius: 9999px;\\n    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);\\n  }\\n\\n  /* Premium glass morphism cards */\\n  .glass-card-premium {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 255, 255, 0.15);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    transition: all 0.3s ease-in-out;\\n  }\\n\\n  .glass-card-premium:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);\\n  }\\n\\n  /* Gold gradient buttons */\\n  .btn-gold-gradient {\\n    background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 1px solid rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);\\n    color: white;\\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .btn-gold-gradient:hover {\\n    transform: translateY(-2px) scale(1.02);\\n    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);\\n  }\\n\\n  /* Shimmer effect for buttons and cards */\\n  .shimmer-effect {\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);\\n    animation: glassShimmer 1.5s ease-in-out infinite;\\n  }\\n\\n  /* Popular badge styling */\\n  .popular-badge {\\n    background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);\\n    border-radius: 25px;\\n  }\\n\\n  /* Tab navigation premium styling */\\n  .tab-navigation-premium {\\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n    border-radius: 16px;\\n  }\\n\\n  /* Premium Gold/Metallic Buttons */\\n  .btn-gold-premium {\\n    background: linear-gradient(135deg,\\n      #8B6914 0%,\\n      #B8860B 15%,\\n      #DAA520 30%,\\n      #FFD700 45%,\\n      #FFED4E 60%,\\n      #FFD700 75%,\\n      #DAA520 90%,\\n      #B8860B 100%);\\n    background-size: 300% 300%;\\n    -webkit-backdrop-filter: blur(20px);\\n    backdrop-filter: blur(20px);\\n    border: 2px solid rgba(255, 215, 0, 0.4);\\n    box-shadow:\\n      0 8px 25px rgba(255, 215, 0, 0.4),\\n      0 4px 15px rgba(184, 134, 11, 0.3),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.3),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.2);\\n    color: #1a1a1a;\\n    font-weight: 700;\\n    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.4);\\n    border-radius: 12px;\\n    padding: 1rem 2rem;\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n    animation: goldShine 4s ease-in-out infinite;\\n  }\\n\\n  .btn-gold-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);\\n    transition: left 0.6s ease;\\n  }\\n\\n  .btn-gold-premium:hover::before {\\n    left: 100%;\\n  }\\n\\n  .btn-gold-premium:hover {\\n    transform: translateY(-3px) scale(1.05);\\n    box-shadow:\\n      0 15px 40px rgba(255, 215, 0, 0.5),\\n      0 8px 25px rgba(184, 134, 11, 0.4),\\n      inset 0 1px 0 rgba(255, 255, 255, 0.4),\\n      inset 0 -1px 0 rgba(139, 105, 20, 0.3);\\n    background-position: 100% 100%;\\n    filter: brightness(1.1) saturate(1.1);\\n    border-color: rgba(255, 215, 0, 0.6);\\n  }\\n\\n  /* Metallic Card Effects */\\n  .card-metallic-gold {\\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);\\n    -webkit-backdrop-filter: blur(25px);\\n    backdrop-filter: blur(25px);\\n    border: 1px solid rgba(255, 215, 0, 0.2);\\n    border-radius: 20px;\\n    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n    transition: all 0.3s ease-in-out;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .card-metallic-gold::before {\\n    content: '';\\n    position: absolute;\\n    top: -50%;\\n    left: -50%;\\n    width: 200%;\\n    height: 200%;\\n    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);\\n    animation: goldGlow 4s ease-in-out infinite;\\n    pointer-events: none;\\n  }\\n\\n  .card-metallic-gold:hover {\\n    transform: translateY(-8px) scale(1.02);\\n    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n    border-color: rgba(255, 215, 0, 0.4);\\n  }\\n  \\n  /* Animation utilities */\\n  .animate-fade-in-up {\\n    animation: fadeInUp 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-down {\\n    animation: fadeInDown 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-left {\\n    animation: fadeInLeft 0.6s ease-out forwards;\\n  }\\n  \\n  .animate-fade-in-right {\\n    animation: fadeInRight 0.6s ease-out forwards;\\n  }\\n}\\n\\n/* Utility classes */\\n@layer utilities {\\n  /* Text direction utilities */\\n  .text-start {\\n    text-align: start;\\n  }\\n  \\n  .text-end {\\n    text-align: end;\\n  }\\n  \\n  /* Margin utilities for RTL */\\n  .ms-auto {\\n    margin-inline-start: auto;\\n  }\\n  \\n  .me-auto {\\n    margin-inline-end: auto;\\n  }\\n  \\n  /* Padding utilities for RTL */\\n  .ps-4 {\\n    padding-inline-start: 1rem;\\n  }\\n  \\n  .pe-4 {\\n    padding-inline-end: 1rem;\\n  }\\n}\\n\\n/* Keyframe animations */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes fadeInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes fadeInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n/* Enhanced keyframe animations for glass effects */\\n@keyframes gradientShift {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGradient {\\n  0%, 100% { background-position: 0% 0%; }\\n  25% { background-position: 100% 0%; }\\n  50% { background-position: 100% 100%; }\\n  75% { background-position: 0% 100%; }\\n}\\n\\n@keyframes glassShimmer {\\n  0% { background-position: -200% 0; }\\n  100% { background-position: 200% 0; }\\n}\\n\\n@keyframes floatGlass {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n    /* Removed blur filter */\\n  }\\n  25% {\\n    transform: translateY(-10px) rotate(1deg);\\n    /* Removed blur filter */\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(0deg);\\n    /* Removed blur filter */\\n  }\\n  75% {\\n    transform: translateY(-10px) rotate(-1deg);\\n    /* Removed blur filter */\\n  }\\n}\\n\\n@keyframes pulseGlass {\\n  0%, 100% {\\n    background: rgba(255, 255, 255, 0.1);\\n    border-color: rgba(255, 255, 255, 0.2);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  }\\n  50% {\\n    background: rgba(255, 255, 255, 0.15);\\n    border-color: rgba(255, 255, 255, 0.3);\\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n/* Premium Gold/Metallic Animations */\\n@keyframes goldShine {\\n  0%, 100% { background-position: 0% 50%; }\\n  50% { background-position: 100% 50%; }\\n}\\n\\n@keyframes premiumGoldShine {\\n  0% { background-position: -200% 0%; }\\n  50% { background-position: 0% 0%; }\\n  100% { background-position: 200% 0%; }\\n}\\n\\n/* Sharp animation without any blur effects */\\n@keyframes premiumGoldShineSharp {\\n  0% {\\n    background-position: -200% 0%;\\n    transform: translateZ(0);\\n  }\\n  50% {\\n    background-position: 0% 0%;\\n    transform: translateZ(0);\\n  }\\n  100% {\\n    background-position: 200% 0%;\\n    transform: translateZ(0);\\n  }\\n}\\n\\n@keyframes goldGlimmer {\\n  0% { transform: translateX(-100%) skewX(-15deg); }\\n  100% { transform: translateX(200%) skewX(-15deg); }\\n}\\n\\n@keyframes textShimmer {\\n  0% {\\n    background-position: -200% 0;\\n    opacity: 0;\\n  }\\n  50% {\\n    background-position: 0% 0;\\n    opacity: 1;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n    opacity: 0;\\n  }\\n}\\n\\n\\n\\n@keyframes goldGlow {\\n  0%, 100% {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: rotate(180deg) scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n\\n/* Reduced motion preferences */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n\\n  .glass,\\n  .glass-card,\\n  .glass-button {\\n    background: white !important;\\n    backdrop-filter: none !important;\\n    -webkit-backdrop-filter: none !important;\\n    border: 1px solid #ccc !important;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvY3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbN10ub25lT2ZbMTNdLnVzZVsxXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbN10ub25lT2ZbMTNdLnVzZVsyXSEuL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ2lJO0FBQzJMO0FBQzVULDhCQUE4QixrSEFBMkI7QUFDekQsMEJBQTBCLDhRQUFpQztBQUMzRDtBQUNBLGdFQUFnRSw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGdCQUFnQiw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGlWQUFpViw0QkFBNEIsNEJBQTRCLGdDQUFnQyxrQ0FBa0MsVUFBVSx3QkFBd0IscUJBQXFCLEdBQUcsK2FBQSthLHNCQUFzQiwyQ0FBMkMsNkJBQTZCLDBCQUEwQixvQkFBb0IseUVBQXlFLDBDQUEwQyw0Q0FBNEMscURBQXFELFVBQVUsZ0tBQWdLLGVBQWUsaUNBQWlDLFVBQVUsMk5BQTJOLGVBQWUsMkJBQTJCLGtDQUFrQyxVQUFVLGlHQUFpRyw4Q0FBOEMsOENBQThDLEdBQUcsa0dBQWtHLHVCQUF1Qix5QkFBeUIsR0FBRyxpRkFBaUYsbUJBQW1CLDZCQUE2QixHQUFHLDJFQUEyRSx3QkFBd0IsR0FBRywwU0FBMFMsNENBQTRDLDBDQUEwQyw0Q0FBNEMsMkJBQTJCLFVBQVUsaUVBQWlFLG1CQUFtQixHQUFHLDJHQUEyRyxtQkFBbUIsbUJBQW1CLHVCQUF1Qiw2QkFBNkIsR0FBRyxTQUFTLG9CQUFvQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsZ2JBQWdiLG9CQUFvQixrQ0FBa0Msc0NBQXNDLFVBQVUsa01BQWtNLDBCQUEwQiwyQ0FBMkMsNkNBQTZDLDRCQUE0QixpQ0FBaUMsaUNBQWlDLG9DQUFvQywyQkFBMkIsc0JBQXNCLHVCQUF1QixVQUFVLDhGQUE4Rix5QkFBeUIsR0FBRywwTkFBME4sZ0NBQWdDLDBDQUEwQyxtQ0FBbUMsVUFBVSwrRkFBK0Ysa0JBQWtCLEdBQUcsK01BQStNLHFCQUFxQixHQUFHLG1GQUFtRiw2QkFBNkIsR0FBRyxpSkFBaUosaUJBQWlCLEdBQUcsNkhBQTZILG1DQUFtQyxpQ0FBaUMsVUFBVSxvR0FBb0csNkJBQTZCLEdBQUcscUtBQXFLLGdDQUFnQywwQkFBMEIsVUFBVSxzRUFBc0UsdUJBQXVCLEdBQUcsNEpBQTRKLGNBQWMsR0FBRyxjQUFjLGNBQWMsZUFBZSxHQUFHLFlBQVksZUFBZSxHQUFHLG9CQUFvQixxQkFBcUIsY0FBYyxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyw2RUFBNkUscUJBQXFCLEdBQUcsa1FBQWtRLGdCQUFnQiwyQkFBMkIsVUFBVSxnREFBZ0QsZ0JBQWdCLDJCQUEyQixVQUFVLCtFQUErRSxvQkFBb0IsR0FBRyxpRkFBaUYsb0JBQW9CLEdBQUcsbWJBQW1iLG9CQUFvQixtQ0FBbUMsVUFBVSx3S0FBd0ssb0JBQW9CLGlCQUFpQixHQUFHLCtIQUErSCxrQkFBa0IsR0FBRyxvUEFBb1AsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsMkJBQTJCLDBCQUEwQixzQkFBc0IsdUJBQXVCLHdCQUF3QiwyQkFBMkIsMkJBQTJCLDBCQUEwQixvQkFBb0Isd0JBQXdCLDJCQUEyQixHQUFHLG1XQUFtVyxtQ0FBbUMsd0JBQXdCLGlEQUFpRCxnQ0FBZ0MsaUNBQWlDLDZCQUE2QixnSEFBZ0gsOEdBQThHLHNGQUFzRiwwQkFBMEIsR0FBRyx5REFBeUQsbUJBQW1CLGVBQWUsR0FBRyw4Q0FBOEMsbUJBQW1CLGVBQWUsR0FBRyw0Q0FBNEMsZUFBZSxHQUFHLG1DQUFtQyxzQkFBc0Isd0JBQXdCLEdBQUcsNkJBQTZCLHlCQUF5QixHQUFHLG9VQUFvVSxtQkFBbUIsc0JBQXNCLEdBQUcsWUFBWSwwUEFBMFAsNkNBQTZDLGlDQUFpQyxpQ0FBaUMsMEJBQTBCLHNDQUFzQyxzQ0FBc0MsR0FBRyx1REFBdUQsOEJBQThCLGlDQUFpQyw2QkFBNkIsNkJBQTZCLDJCQUEyQixzQ0FBc0Msc0NBQXNDLEdBQUcsc0NBQXNDLDZCQUE2Qiw2QkFBNkIsNkJBQTZCLGVBQWUsc0NBQXNDLHNDQUFzQywwQkFBMEIsMkJBQTJCLGtDQUFrQyw4QkFBOEIsOEJBQThCLDhCQUE4QixtQkFBbUIsaUJBQWlCLGdCQUFnQixtQkFBbUIsMkJBQTJCLDBCQUEwQixzQkFBc0IsMkJBQTJCLEdBQUcsdUJBQXVCLHVCQUF1QixHQUFHLG9CQUFvQix3QkFBd0IsR0FBRyxrREFBa0QsbUNBQW1DLHdCQUF3QixpREFBaUQsZ0NBQWdDLGlDQUFpQyw2QkFBNkIsZ0hBQWdILDhHQUE4RyxzRkFBc0YsR0FBRyxzREFBc0QsOEJBQThCLG1DQUFtQywrQkFBK0IsZ0NBQWdDLGlDQUFpQyxHQUFHLCtCQUErQiw2UUFBNlEsR0FBRyxxQ0FBcUMsaUNBQWlDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyw0QkFBNEIsMktBQTJLLEdBQUcscUNBQXFDLDhCQUE4QiwrQkFBK0IsK0JBQStCLCtCQUErQixLQUFLLEdBQUcsK0hBQStILDhCQUE4QixtQ0FBbUMsR0FBRyxxQ0FBcUMsOE9BQThPLDhCQUE4QixtQ0FBbUMsK0JBQStCLGdDQUFnQyxpQ0FBaUMsR0FBRyxxQ0FBcUMsdUNBQXVDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyxpRkFBaUYsOEJBQThCLG1DQUFtQyxHQUFHLG1CQUFtQixzQkFBc0IsMEJBQTBCLG9CQUFvQixxQkFBcUIsZUFBZSxxQkFBcUIseUJBQXlCLEdBQUcseUJBQXlCLGtDQUFrQywrQ0FBK0MsR0FBRyxVQUFVLDhCQUE4QixLQUFLLGNBQWMsdUJBQXVCLDhEQUE4RCx5QkFBeUIsd0RBQXdELG9HQUFvRyw2REFBNkQsK0JBQStCLG9DQUFvQyxpQ0FBaUMsR0FBRyxpRUFBaUUsd0ZBQXdGLDBEQUEwRCx5Q0FBeUMsS0FBSyxtQkFBbUIsb0dBQW9HLDRFQUE0RSxLQUFLLHFCQUFxQixzR0FBc0csZ0VBQWdFLEtBQUssb0RBQW9ELHdCQUF3QixLQUFLLHVEQUF1RCxpQkFBaUIsS0FBSyxtQ0FBbUMsdUJBQXVCLGlFQUFpRSxHQUFHLCtDQUErQyx1QkFBdUIsOERBQThELEdBQUcsbUNBQW1DLDBCQUEwQix1QkFBdUIsaUVBQWlFLEdBQUcsK0NBQStDLHVCQUF1Qiw4REFBOEQsR0FBRyx5Q0FBeUMsdUJBQXVCLGlFQUFpRSxHQUFHLHFEQUFxRCx1QkFBdUIsaUVBQWlFLEdBQUcsY0FBYyxnQkFBZ0IsR0FBRyw2QkFBNkIsa0JBQWtCLHVCQUF1QixLQUFLLEdBQUcsNkJBQTZCLGtCQUFrQix1QkFBdUIsS0FBSyxHQUFHLDhCQUE4QixrQkFBa0Isd0JBQXdCLEtBQUssR0FBRyw4QkFBOEIsa0JBQWtCLHdCQUF3QixLQUFLLEdBQUcsOEJBQThCLGtCQUFrQix3QkFBd0IsS0FBSyxHQUFHLFVBQVUseUNBQXlDLGdDQUFnQyx3Q0FBd0MsK0NBQStDLDhDQUE4QyxxQ0FBcUMsR0FBRyxnQkFBZ0IsMENBQTBDLGdDQUFnQyxnREFBZ0QsR0FBRyxpQkFBaUIseUNBQXlDLGdDQUFnQyx3Q0FBd0MsK0NBQStDLHdCQUF3Qix1QkFBdUIscUNBQXFDLG9CQUFvQixHQUFHLHVCQUF1Qix5Q0FBeUMsNENBQTRDLCtDQUErQyxHQUFHLGVBQWUsMENBQTBDLGdDQUFnQyx3Q0FBd0MsZ0RBQWdELHdCQUF3QixrQkFBa0IsOENBQThDLHFDQUFxQyxHQUFHLHFCQUFxQiwwQ0FBMEMsZ0NBQWdDLGdEQUFnRCxHQUFHLG1FQUFtRSxvTUFBb00sMkJBQTJCLDBFQUEwRSxpRUFBaUUsd0VBQXdFLHdFQUF3RSw2REFBNkQsdUJBQXVCLHdCQUF3QixzQkFBc0IseUJBQXlCLHFCQUFxQix5QkFBeUIsd0RBQXdELG9GQUFvRix3R0FBd0csNEdBQTRHLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsc0JBQXNCLCtCQUErQix1QkFBdUIsdUJBQXVCLG9NQUFvTSxpRUFBaUUsd0VBQXdFLHdFQUF3RSw2REFBNkQscUZBQXFGLHlHQUF5Ryw0R0FBNEcsR0FBRyxnQkFBZ0IsMENBQTBDLGtDQUFrQyxpREFBaUQsS0FBSyxrQkFBa0Isb01BQW9NLDJCQUEyQixzQkFBc0IseUNBQXlDLDZDQUE2Qyx1QkFBdUIsd0JBQXdCLHNCQUFzQix5QkFBeUIscUJBQXFCLHlCQUF5QixzREFBc0Qsa0ZBQWtGLHNHQUFzRyw0R0FBNEcsNkJBQTZCLDZEQUE2RCwrQkFBK0IsR0FBRyx3QkFBd0IsK0JBQStCLG9NQUFvTSx5Q0FBeUMsNkNBQTZDLG9GQUFvRix3R0FBd0csNEdBQTRHLEdBQUcsOEJBQThCLHlCQUF5Qix1REFBdUQsR0FBRyxrQkFBa0IsMENBQTBDLGtDQUFrQyxLQUFLLGdCQUFnQiwyQkFBMkIsc0JBQXNCLHlDQUF5Qyw4Q0FBOEMseUJBQXlCLDBCQUEwQix5QkFBeUIsNEJBQTRCLHFCQUFxQix5QkFBeUIscURBQXFELDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsc0JBQXNCLHlDQUF5Qyw2Q0FBNkMsR0FBRyw0QkFBNEIsc0NBQXNDLHlCQUF5Qix3REFBd0QsR0FBRyxrQ0FBa0MseUNBQXlDLEdBQUcsZ0JBQWdCLDBDQUEwQyxrQ0FBa0MsS0FBSyxtREFBbUQsb01BQW9NLDJCQUEyQiw2Q0FBNkMsdUJBQXVCLHdCQUF3QixzQkFBc0IseUJBQXlCLHFCQUFxQix5QkFBeUIsd0RBQXdELDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsb0JBQW9CLCtCQUErQix1QkFBdUIsdUJBQXVCLG9NQUFvTSw2Q0FBNkMsR0FBRyxjQUFjLDBDQUEwQyxrQ0FBa0MsaURBQWlELGdEQUFnRCxLQUFLLG9CQUFvQixrREFBa0QsS0FBSyxzSEFBc0gsd0dBQXdHLDBDQUEwQyxrQ0FBa0MsbURBQW1ELDBCQUEwQix5QkFBeUIsc0JBQXNCLHVCQUF1Qix3REFBd0QseUJBQXlCLHVCQUF1QixnREFBZ0QsS0FBSyx5QkFBeUIsa0JBQWtCLHlCQUF5QixhQUFhLGtCQUFrQixrQkFBa0IsbUJBQW1CLDBHQUEwRyxpQ0FBaUMsS0FBSywrQkFBK0IsaUJBQWlCLEtBQUssdUJBQXVCLDhDQUE4Qyx3R0FBd0csNkNBQTZDLDRGQUE0RixLQUFLLGtFQUFrRSx3R0FBd0csMENBQTBDLGtDQUFrQyxrREFBa0QsMEJBQTBCLGdEQUFnRCx3REFBd0QsS0FBSyxxQkFBcUIsd0dBQXdHLDhDQUE4QyxrREFBa0QsOENBQThDLEtBQUssZ0VBQWdFLDBCQUEwQiw2QkFBNkIsNkRBQTZELCtCQUErQix1R0FBdUcsMENBQTBDLGtDQUFrQyxpREFBaUQseUZBQXlGLEdBQUcsdUJBQXVCLDhDQUE4QywwRkFBMEYsS0FBSywrREFBK0Qsc0JBQXNCLHlCQUF5QixHQUFHLDhCQUE4Qix3QkFBd0Isd0JBQXdCLDJCQUEyQixLQUFLLEdBQUcsc0JBQXNCLHVCQUF1Qix3QkFBd0IsR0FBRyw2QkFBNkIsMEJBQTBCLDJCQUEyQiw0QkFBNEIsS0FBSyxHQUFHLDhCQUE4QiwwQkFBMEIseUJBQXlCLDBCQUEwQixLQUFLLEdBQUcsd0ZBQXdGLDJDQUEyQyx1QkFBdUIsd0JBQXdCLDhCQUE4QixvR0FBb0csNEVBQTRFLDhGQUE4RixpRkFBaUYsNEJBQTRCLG9DQUFvQywyQ0FBMkMsaURBQWlELHdEQUF3RCxLQUFLLGVBQWUscUVBQXFFLHdCQUF3Qix5QkFBeUIscUJBQXFCLDZCQUE2QixHQUFHLDhCQUE4QixtQkFBbUIseUJBQXlCLDBCQUEwQixLQUFLLEdBQUcsZUFBZSxzQkFBc0Isc0JBQXNCLHFCQUFxQiw2QkFBNkIsR0FBRyw4QkFBOEIsbUJBQW1CLDBCQUEwQiwyQkFBMkIsS0FBSyxHQUFHLGVBQWUsdUJBQXVCLHlCQUF5QixxQkFBcUIsR0FBRyw4QkFBOEIsbUJBQW1CLHdCQUF3Qix3QkFBd0IsS0FBSyxHQUFHLHdEQUF3RCwwRUFBMEUsaUVBQWlFLHdFQUF3RSx3RUFBd0UsMEVBQTBFLGlIQUFpSCw2REFBNkQsa0NBQWtDLGtDQUFrQyx1QkFBdUIsaUNBQWlDLHVEQUF1RCxHQUFHLGlFQUFpRSwyR0FBMkcsaUNBQWlDLG9DQUFvQyw0QkFBNEIsMkNBQTJDLG1EQUFtRCxxREFBcUQsNERBQTRELEtBQUssOEZBQThGLHFRQUFxUSxpQ0FBaUMsb0NBQW9DLDRCQUE0QiwyQ0FBMkMsK0RBQStELG1HQUFtRyw0REFBNEQseUJBQXlCLDBGQUEwRiwwQ0FBMEMseUNBQXlDLG9EQUFvRCwrQkFBK0IsdUNBQXVDLGtDQUFrQyxLQUFLLGtEQUFrRCxvR0FBb0csNEVBQTRFLDhGQUE4Rix3RkFBd0YsMERBQTBELHlDQUF5QyxLQUFLLHdCQUF3QixvR0FBb0csNEVBQTRFLDhGQUE4Rix3RkFBd0YsMERBQTBELHlDQUF5QyxpQ0FBaUMsb0RBQW9ELDhCQUE4Qix1QkFBdUIsdUJBQXVCLEtBQUssbVVBQW1VLDBNQUEwTSxpQ0FBaUMsMENBQTBDLGtDQUFrQywrQ0FBK0Msb01BQW9NLHFCQUFxQix1QkFBdUIsc0RBQXNELDBCQUEwQix5QkFBeUIsdUNBQXVDLHlCQUF5Qix1QkFBdUIsbURBQW1ELEtBQUssNkJBQTZCLGtCQUFrQix5QkFBeUIsYUFBYSxrQkFBa0Isa0JBQWtCLG1CQUFtQix5R0FBeUcsaUNBQWlDLEtBQUssbUNBQW1DLGlCQUFpQixLQUFLLDJCQUEyQiw4Q0FBOEMscU1BQXFNLHFDQUFxQyw0Q0FBNEMsMkNBQTJDLEtBQUssb0RBQW9ELG1HQUFtRywwQ0FBMEMsa0NBQWtDLCtDQUErQywwQkFBMEIsNEZBQTRGLHVDQUF1Qyx5QkFBeUIsdUJBQXVCLEtBQUssK0JBQStCLGtCQUFrQix5QkFBeUIsZ0JBQWdCLGlCQUFpQixrQkFBa0IsbUJBQW1CLHNGQUFzRixrREFBa0QsMkJBQTJCLEtBQUssNkJBQTZCLDhDQUE4Qyw2RkFBNkYsMkNBQTJDLEtBQUssbURBQW1ELHlCQUF5QixHQUFHLFlBQVksd0JBQXdCLEdBQUcsV0FBVyxxQkFBcUIsR0FBRyxVQUFVLG9CQUFvQixHQUFHLGFBQWEsdUJBQXVCLEdBQUcsYUFBYSx1QkFBdUIsR0FBRyxhQUFhLGlCQUFpQixHQUFHLFlBQVksZUFBZSxHQUFHLGFBQWEsb0JBQW9CLEdBQUcsYUFBYSxtQkFBbUIsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFdBQVcsaUJBQWlCLEdBQUcsV0FBVyxpQkFBaUIsR0FBRyxhQUFhLGdCQUFnQixHQUFHLGNBQWMsaUJBQWlCLEdBQUcsY0FBYyxpQkFBaUIsR0FBRyxjQUFjLGlCQUFpQixHQUFHLGNBQWMsa0JBQWtCLEdBQUcsYUFBYSxtQkFBbUIsR0FBRyxhQUFhLGlCQUFpQixHQUFHLFdBQVcsY0FBYyxHQUFHLGVBQWUsY0FBYyxHQUFHLFlBQVksaUJBQWlCLEdBQUcsWUFBWSxlQUFlLEdBQUcsWUFBWSxpQkFBaUIsR0FBRyxZQUFZLGVBQWUsR0FBRyxZQUFZLGVBQWUsR0FBRyxXQUFXLGlCQUFpQixHQUFHLFdBQVcsZUFBZSxHQUFHLFlBQVksZUFBZSxHQUFHLFlBQVksbUJBQW1CLEdBQUcsZ0JBQWdCLGVBQWUsR0FBRyxhQUFhLGtCQUFrQixHQUFHLGFBQWEsZ0JBQWdCLEdBQUcsYUFBYSxnQkFBZ0IsR0FBRyxhQUFhLGdCQUFnQixHQUFHLFlBQVksZ0JBQWdCLEdBQUcsWUFBWSxrQkFBa0IsR0FBRyxlQUFlLGdCQUFnQixHQUFHLFVBQVUsYUFBYSxHQUFHLFVBQVUsaUJBQWlCLEdBQUcsY0FBYyxhQUFhLEdBQUcsV0FBVyxjQUFjLEdBQUcsV0FBVyxjQUFjLEdBQUcsV0FBVyxjQUFjLEdBQUcsV0FBVyxjQUFjLEdBQUcsVUFBVSxjQUFjLEdBQUcsV0FBVyxlQUFlLEdBQUcsVUFBVSxnQkFBZ0IsR0FBRyxZQUFZLHVCQUF1QixHQUFHLFVBQVUsaUJBQWlCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxTQUFTLGdCQUFnQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsaUJBQWlCLGtCQUFrQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsU0FBUyxvQkFBb0IsR0FBRyxZQUFZLHNCQUFzQix1QkFBdUIsR0FBRyxTQUFTLDJCQUEyQixHQUFHLFVBQVUsMEJBQTBCLEdBQUcsVUFBVSx3QkFBd0IsR0FBRyxVQUFVLHdCQUF3QixHQUFHLFNBQVMsMEJBQTBCLEdBQUcsVUFBVSx3QkFBd0IsR0FBRyxTQUFTLDJCQUEyQixHQUFHLFNBQVMsd0JBQXdCLEdBQUcsU0FBUywwQkFBMEIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLFNBQVMsNEJBQTRCLEdBQUcsU0FBUywwQkFBMEIsR0FBRyxhQUFhLHlCQUF5QixHQUFHLFVBQVUscUJBQXFCLEdBQUcsU0FBUyx1QkFBdUIsR0FBRyxVQUFVLHFCQUFxQixHQUFHLFNBQVMscUJBQXFCLEdBQUcsU0FBUyx1QkFBdUIsR0FBRyxTQUFTLHFCQUFxQixHQUFHLFVBQVUsbUJBQW1CLEdBQUcsaUJBQWlCLDBCQUEwQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsZ0JBQWdCLHlCQUF5QixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsV0FBVyxrQkFBa0IsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFlBQVkscUJBQXFCLEdBQUcsU0FBUyxtQkFBbUIsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFNBQVMsbUJBQW1CLEdBQUcsU0FBUyxpQkFBaUIsR0FBRyxRQUFRLG1CQUFtQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsU0FBUyxpQkFBaUIsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFFBQVEsb0JBQW9CLEdBQUcsU0FBUyxpQkFBaUIsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFFBQVEsaUJBQWlCLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFFBQVEsbUJBQW1CLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFFBQVEsaUJBQWlCLEdBQUcsV0FBVyxpQkFBaUIsR0FBRyxZQUFZLHFCQUFxQixHQUFHLGlCQUFpQixzQkFBc0IsR0FBRyxRQUFRLG1CQUFtQixHQUFHLFlBQVksb0JBQW9CLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxTQUFTLGdCQUFnQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxTQUFTLGdCQUFnQixHQUFHLFFBQVEsbUJBQW1CLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxTQUFTLGdCQUFnQixHQUFHLFFBQVEsZ0JBQWdCLEdBQUcsU0FBUyxpQkFBaUIsR0FBRyxRQUFRLG1CQUFtQixHQUFHLFFBQVEsa0JBQWtCLEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxRQUFRLGdCQUFnQixHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxjQUFjLHFCQUFxQixHQUFHLGNBQWMscUJBQXFCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxjQUFjLHFCQUFxQixHQUFHLGNBQWMscUJBQXFCLEdBQUcsYUFBYSxxQkFBcUIsR0FBRyxhQUFhLHFCQUFxQixHQUFHLFdBQVcsaUJBQWlCLEdBQUcsa0JBQWtCLG1CQUFtQixHQUFHLHVCQUF1QiwyQkFBMkIsb01BQW9NLEdBQUcsc0JBQXNCLDRCQUE0QixvTUFBb00sR0FBRyx1QkFBdUIsMkJBQTJCLG9NQUFvTSxHQUFHLGVBQWUsd0JBQXdCLG9NQUFvTSxHQUFHLGNBQWMsdUJBQXVCLHVCQUF1QixvTUFBb00sR0FBRyxjQUFjLG9CQUFvQixvTUFBb00sR0FBRyxjQUFjLG9NQUFvTSxHQUFHLG9CQUFvQixXQUFXLGtCQUFrQixLQUFLLEdBQUcsa0JBQWtCLDhEQUE4RCxHQUFHLG1CQUFtQixVQUFVLGdDQUFnQyxLQUFLLEdBQUcsaUJBQWlCLHVDQUF1QyxHQUFHLHVCQUF1Qix3QkFBd0IsR0FBRyxtQkFBbUIsb0JBQW9CLEdBQUcsZ0JBQWdCLDhCQUE4Qiw4QkFBOEIsOEJBQThCLEdBQUcsZ0JBQWdCLGlCQUFpQixHQUFHLGdCQUFnQixxREFBcUQsR0FBRyxnQkFBZ0IscURBQXFELEdBQUcsYUFBYSwyQkFBMkIsR0FBRyxnQkFBZ0IsNEJBQTRCLEdBQUcsaUJBQWlCLHdCQUF3QixHQUFHLG1CQUFtQiwwQkFBMEIsR0FBRyxtQkFBbUIsNEJBQTRCLEdBQUcsb0JBQW9CLG1DQUFtQyxHQUFHLFdBQVcsY0FBYyxHQUFHLFdBQVcsY0FBYyxHQUFHLFVBQVUsZ0JBQWdCLEdBQUcsVUFBVSxpQkFBaUIsR0FBRyxVQUFVLGNBQWMsR0FBRyxVQUFVLGdCQUFnQixHQUFHLFVBQVUsY0FBYyxHQUFHLGdEQUFnRCw0QkFBNEIsNERBQTRELHFFQUFxRSxHQUFHLGdEQUFnRCw0QkFBNEIsMkRBQTJELG9FQUFvRSxHQUFHLGdEQUFnRCw0QkFBNEIsNERBQTRELHFFQUFxRSxHQUFHLGdEQUFnRCw0QkFBNEIseURBQXlELGtFQUFrRSxHQUFHLGdEQUFnRCw0QkFBNEIseURBQXlELGtFQUFrRSxHQUFHLGdEQUFnRCw0QkFBNEIsb0VBQW9FLDZEQUE2RCxHQUFHLGdEQUFnRCw0QkFBNEIsaUVBQWlFLDBEQUEwRCxHQUFHLGdEQUFnRCw0QkFBNEIsbUVBQW1FLDREQUE0RCxHQUFHLGdEQUFnRCw0QkFBNEIsaUVBQWlFLDBEQUEwRCxHQUFHLG9CQUFvQixxQkFBcUIsR0FBRyxzQkFBc0Isd0JBQXdCLEdBQUcsWUFBWSwyQkFBMkIsR0FBRyxnQkFBZ0Isd0JBQXdCLEdBQUcsZ0JBQWdCLDBCQUEwQixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxlQUFlLDBCQUEwQixHQUFHLGVBQWUsNEJBQTRCLEdBQUcsZUFBZSwyQkFBMkIsR0FBRyxXQUFXLHNCQUFzQixHQUFHLGFBQWEsc0JBQXNCLEdBQUcsYUFBYSwwQkFBMEIsR0FBRyxvQkFBb0IsMkJBQTJCLGlFQUFpRSxHQUFHLG9CQUFvQiwyQkFBMkIsaUVBQWlFLEdBQUcsb0JBQW9CLDJCQUEyQiw4REFBOEQsR0FBRyxzQkFBc0IseUNBQXlDLEdBQUcseUJBQXlCLGtDQUFrQyxHQUFHLGdCQUFnQix1QkFBdUIsZ0VBQWdFLEdBQUcsZ0JBQWdCLHVCQUF1QiwrREFBK0QsR0FBRyxnQkFBZ0IsdUJBQXVCLGlFQUFpRSxHQUFHLGVBQWUsdUJBQXVCLGlFQUFpRSxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsb0JBQW9CLHVCQUF1QixpRUFBaUUsR0FBRyxtQkFBbUIsa0NBQWtDLEdBQUcsYUFBYSx1QkFBdUIsaUVBQWlFLEdBQUcsa0JBQWtCLDZDQUE2QyxHQUFHLGlCQUFpQiw4Q0FBOEMsR0FBRyxrQkFBa0IsOENBQThDLEdBQUcsc0JBQXNCLGlGQUFpRixHQUFHLHFCQUFxQiwwRUFBMEUsR0FBRyxrQkFBa0IsaUVBQWlFLHlFQUF5RSx3RUFBd0UsR0FBRyxvQkFBb0IsaUVBQWlFLDBFQUEwRSx3RUFBd0UsR0FBRyxxQkFBcUIsaUVBQWlFLHlFQUF5RSx3RUFBd0UsR0FBRyx5QkFBeUIsK0VBQStFLHlFQUF5RSx3RUFBd0UsR0FBRyxxQkFBcUIscUVBQXFFLG9FQUFvRSx3RUFBd0UsR0FBRyx5QkFBeUIsOEVBQThFLHdFQUF3RSx3RUFBd0UsR0FBRyxpQkFBaUIseUVBQXlFLGlIQUFpSCxHQUFHLG1CQUFtQiwyRUFBMkUsZ0lBQWdJLEdBQUcscUJBQXFCLDJFQUEyRSxHQUFHLHVCQUF1QiwyRUFBMkUsR0FBRyxtQkFBbUIsNkRBQTZELEdBQUcsb0JBQW9CLDZEQUE2RCxHQUFHLHFCQUFxQiw2REFBNkQsR0FBRyxtQkFBbUIsaUVBQWlFLEdBQUcsUUFBUSxxQkFBcUIsR0FBRyxTQUFTLGtCQUFrQixHQUFHLFFBQVEsb0JBQW9CLEdBQUcsUUFBUSxxQkFBcUIsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFFBQVEsb0JBQW9CLEdBQUcsUUFBUSxrQkFBa0IsR0FBRyxTQUFTLDBCQUEwQiwyQkFBMkIsR0FBRyxVQUFVLHlCQUF5QiwwQkFBMEIsR0FBRyxVQUFVLHVCQUF1Qix3QkFBd0IsR0FBRyxTQUFTLHlCQUF5QiwwQkFBMEIsR0FBRyxTQUFTLHVCQUF1Qix3QkFBd0IsR0FBRyxTQUFTLHlCQUF5QiwwQkFBMEIsR0FBRyxTQUFTLHVCQUF1Qix3QkFBd0IsR0FBRyxhQUFhLDBCQUEwQiw2QkFBNkIsR0FBRyxTQUFTLHlCQUF5Qiw0QkFBNEIsR0FBRyxTQUFTLHdCQUF3QiwyQkFBMkIsR0FBRyxVQUFVLHNCQUFzQix5QkFBeUIsR0FBRyxTQUFTLHlCQUF5Qiw0QkFBNEIsR0FBRyxTQUFTLHNCQUFzQix5QkFBeUIsR0FBRyxTQUFTLHlCQUF5Qiw0QkFBNEIsR0FBRyxTQUFTLHdCQUF3QiwyQkFBMkIsR0FBRyxTQUFTLHNCQUFzQix5QkFBeUIsR0FBRyxhQUFhLDBCQUEwQiw2QkFBNkIsR0FBRyxTQUFTLHlCQUF5QixHQUFHLFNBQVMsc0JBQXNCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxnQkFBZ0IsdUJBQXVCLEdBQUcsZUFBZSxzQkFBc0IsR0FBRyxlQUFlLHNCQUFzQixHQUFHLGVBQWUsNEZBQTRGLEdBQUcsaUJBQWlCLHFFQUFxRSxHQUFHLGNBQWMsaUVBQWlFLEdBQUcsaUJBQWlCLDhGQUE4RixHQUFHLGFBQWEsc0JBQXNCLHNCQUFzQixHQUFHLGFBQWEsd0JBQXdCLHlCQUF5QixHQUFHLGFBQWEsdUJBQXVCLHdCQUF3QixHQUFHLGFBQWEsb0JBQW9CLG1CQUFtQixHQUFHLGNBQWMsb0JBQW9CLHdCQUF3QixHQUFHLFlBQVksd0JBQXdCLHlCQUF5QixHQUFHLFlBQVksd0JBQXdCLHlCQUF5QixHQUFHLFlBQVksdUJBQXVCLHlCQUF5QixHQUFHLFlBQVksdUJBQXVCLHNCQUFzQixHQUFHLGVBQWUscUJBQXFCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxnQkFBZ0IscUJBQXFCLEdBQUcsa0JBQWtCLHFCQUFxQixHQUFHLG9CQUFvQix1QkFBdUIsR0FBRyxtQkFBbUIsNkJBQTZCLEdBQUcsa0JBQWtCLHlCQUF5Qix3REFBd0QsR0FBRyxrQkFBa0IseUJBQXlCLHdEQUF3RCxHQUFHLGtCQUFrQix5QkFBeUIscURBQXFELEdBQUcsa0JBQWtCLHlCQUF5QixxREFBcUQsR0FBRyxrQkFBa0IseUJBQXlCLHFEQUFxRCxHQUFHLHFCQUFxQix5QkFBeUIsc0RBQXNELEdBQUcsb0JBQW9CLHlCQUF5Qix3REFBd0QsR0FBRyxpQkFBaUIseUJBQXlCLHdEQUF3RCxHQUFHLGlCQUFpQix5QkFBeUIsd0RBQXdELEdBQUcscUJBQXFCLHlCQUF5QixzREFBc0QsR0FBRyxlQUFlLHlCQUF5Qix3REFBd0QsR0FBRyxvQkFBb0Isa0NBQWtDLEdBQUcsb0JBQW9CLGtDQUFrQyxHQUFHLG9CQUFvQixrQ0FBa0MsR0FBRyxvQkFBb0Isa0NBQWtDLEdBQUcsb0JBQW9CLGtDQUFrQyxHQUFHLG9CQUFvQixrQ0FBa0MsR0FBRyxvQkFBb0IsbUNBQW1DLEdBQUcsb0JBQW9CLHlCQUF5Qix1REFBdUQsR0FBRyxjQUFjLG9DQUFvQyxHQUFHLDBCQUEwQixrREFBa0QsR0FBRyx1QkFBdUIsK0JBQStCLEdBQUcsZ0JBQWdCLHdDQUF3Qyx1Q0FBdUMsR0FBRywyQ0FBMkMsZ0NBQWdDLCtEQUErRCxHQUFHLHNDQUFzQyxnQ0FBZ0MsK0RBQStELEdBQUcsY0FBYyxlQUFlLEdBQUcsZUFBZSxpQkFBaUIsR0FBRyxlQUFlLGtCQUFrQixHQUFHLGVBQWUsaUJBQWlCLEdBQUcsZUFBZSxrQkFBa0IsR0FBRyxlQUFlLGlCQUFpQixHQUFHLGVBQWUsaUJBQWlCLEdBQUcsZUFBZSxpQkFBaUIsR0FBRyxXQUFXLCtFQUErRSxtR0FBbUcsNEdBQTRHLEdBQUcsZUFBZSxxREFBcUQsa0VBQWtFLDRHQUE0RyxHQUFHLGNBQWMsb0ZBQW9GLHdHQUF3Ryw0R0FBNEcsR0FBRyxXQUFXLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcsb0JBQW9CLHlCQUF5QixrRUFBa0UsR0FBRyxvQkFBb0IseUJBQXlCLGlFQUFpRSxHQUFHLFNBQVMseUJBQXlCLHNMQUFzTCxHQUFHLGdCQUFnQix1R0FBdUcsc0xBQXNMLEdBQUcsbUJBQW1CLHdHQUF3RyxzTEFBc0wsR0FBRyxjQUFjLG9DQUFvQyxzTEFBc0wsR0FBRyxXQUFXLHNMQUFzTCxHQUFHLHFCQUFxQixtQ0FBbUMsb1JBQW9SLDRRQUE0USxHQUFHLGVBQWUscUtBQXFLLDZKQUE2SixzTEFBc0wsNkRBQTZELCtCQUErQixHQUFHLG1CQUFtQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLHNCQUFzQixvR0FBb0csNkRBQTZELCtCQUErQixHQUFHLHVCQUF1QixpQ0FBaUMsNkRBQTZELCtCQUErQixHQUFHLHlCQUF5QixtQ0FBbUMsNkRBQTZELCtCQUErQixHQUFHLGtCQUFrQixnQ0FBZ0MsR0FBRyxpQkFBaUIsK0JBQStCLEdBQUcsaUJBQWlCLCtCQUErQixHQUFHLGlCQUFpQiwrQkFBK0IsR0FBRyxnQkFBZ0IsNkRBQTZELEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxRQUFRLG1CQUFtQixHQUFHLCtDQUErQyx3QkFBd0IsS0FBSyxxS0FBcUsseUZBQXlGLDRCQUE0Qiw0QkFBNEIsNEJBQTRCLDhCQUE4Qix5Q0FBeUMsNkNBQTZDLHdCQUF3QiwyQkFBMkIsK0dBQStHLDhDQUE4QyxrREFBa0QsNkRBQTZELG1DQUFtQyxrQ0FBa0MscURBQXFELGlDQUFpQyxpREFBaUQsaU1BQWlNLDRGQUE0RiwrREFBK0QsR0FBRyw0QkFBNEIsMEJBQTBCLDRCQUE0Qiw0QkFBNEIsNEJBQTRCLDhCQUE4Qix5Q0FBeUMsdUNBQXVDLHdCQUF3QiwyQkFBMkIsR0FBRyw0SEFBNEgsVUFBVSxpQkFBaUIsa0NBQWtDLEtBQUssUUFBUSxpQkFBaUIsK0JBQStCLEtBQUssR0FBRywyQkFBMkIsVUFBVSxpQkFBaUIsbUNBQW1DLEtBQUssUUFBUSxpQkFBaUIsK0JBQStCLEtBQUssR0FBRywyQkFBMkIsVUFBVSxpQkFBaUIsbUNBQW1DLEtBQUssUUFBUSxpQkFBaUIsK0JBQStCLEtBQUssR0FBRyw0QkFBNEIsVUFBVSxpQkFBaUIsa0NBQWtDLEtBQUssUUFBUSxpQkFBaUIsK0JBQStCLEtBQUssR0FBRyxvRkFBb0YsZUFBZSw4QkFBOEIsVUFBVSxnQ0FBZ0MsR0FBRyxnQ0FBZ0MsZUFBZSw2QkFBNkIsVUFBVSwrQkFBK0IsVUFBVSxpQ0FBaUMsVUFBVSwrQkFBK0IsR0FBRyw2QkFBNkIsU0FBUywrQkFBK0IsV0FBVyw4QkFBOEIsR0FBRywyQkFBMkIsY0FBYyw4Q0FBOEMsb0NBQW9DLFNBQVMsZ0RBQWdELG9DQUFvQyxTQUFTLGdEQUFnRCxvQ0FBb0MsU0FBUyxpREFBaUQsb0NBQW9DLEdBQUcsMkJBQTJCLGNBQWMsMkNBQTJDLDZDQUE2QyxnREFBZ0QsS0FBSyxTQUFTLDRDQUE0Qyw2Q0FBNkMsa0RBQWtELEtBQUssR0FBRyxrRUFBa0UsZUFBZSw4QkFBOEIsVUFBVSxnQ0FBZ0MsR0FBRyxpQ0FBaUMsU0FBUyxnQ0FBZ0MsVUFBVSw2QkFBNkIsV0FBVywrQkFBK0IsR0FBRyxzRkFBc0YsUUFBUSxvQ0FBb0MsK0JBQStCLEtBQUssU0FBUyxpQ0FBaUMsK0JBQStCLEtBQUssVUFBVSxtQ0FBbUMsK0JBQStCLEtBQUssR0FBRyw0QkFBNEIsU0FBUyw2Q0FBNkMsV0FBVyw0Q0FBNEMsR0FBRyw0QkFBNEIsUUFBUSxtQ0FBbUMsaUJBQWlCLEtBQUssU0FBUyxnQ0FBZ0MsaUJBQWlCLEtBQUssVUFBVSxrQ0FBa0MsaUJBQWlCLEtBQUssR0FBRyw2QkFBNkIsY0FBYyx1Q0FBdUMsbUJBQW1CLEtBQUssU0FBUywyQ0FBMkMsbUJBQW1CLEtBQUssR0FBRywrRUFBK0Usa0NBQWtDLDRDQUE0Qyw4Q0FBOEMsNkNBQTZDLHVDQUF1QyxLQUFLLEdBQUcsc0NBQXNDLGVBQWUsK0JBQStCLEtBQUssZ0RBQWdELG1DQUFtQyx1Q0FBdUMsK0NBQStDLHdDQUF3QyxLQUFLLEdBQUcsNEJBQTRCLHVCQUF1Qix1QkFBdUIsb01BQW9NLEdBQUcsOEJBQThCLHVCQUF1QixpRUFBaUUsR0FBRywrQkFBK0IsOENBQThDLEdBQUcsbUNBQW1DLHlCQUF5QixzREFBc0QsR0FBRyxtQ0FBbUMseUJBQXlCLHNEQUFzRCxHQUFHLDZCQUE2Qix5QkFBeUIsd0RBQXdELEdBQUcsNEJBQTRCLG9DQUFvQyxHQUFHLCtCQUErQixtQ0FBbUMsd0JBQXdCLEdBQUcseUJBQXlCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcsa0NBQWtDLDRDQUE0QyxHQUFHLGdEQUFnRCwyQkFBMkIsb01BQW9NLEdBQUcseUNBQXlDLHVCQUF1QixvTUFBb00sR0FBRyx5Q0FBeUMsdUJBQXVCLHVCQUF1QixvTUFBb00sR0FBRyx5Q0FBeUMsc0JBQXNCLHNCQUFzQixvTUFBb00sR0FBRywyQ0FBMkMsb0JBQW9CLG9NQUFvTSxHQUFHLDJDQUEyQyxlQUFlLEdBQUcsMENBQTBDLGlCQUFpQixHQUFHLHVDQUF1QywyQkFBMkIsOERBQThELEdBQUcsdUNBQXVDLDJCQUEyQiw4REFBOEQsR0FBRyxtQ0FBbUMsdUJBQXVCLDhEQUE4RCxHQUFHLG1DQUFtQyx1QkFBdUIsOERBQThELEdBQUcsd0NBQXdDLDJDQUEyQyxHQUFHLDJDQUEyQywyQ0FBMkMsR0FBRyw2Q0FBNkMsNENBQTRDLEdBQUcscUNBQXFDLGlFQUFpRSx1RUFBdUUsd0VBQXdFLEdBQUcsbUNBQW1DLDZEQUE2RCxHQUFHLHFDQUFxQyx5QkFBeUIsd0RBQXdELEdBQUcscUNBQXFDLHlCQUF5Qix3REFBd0QsR0FBRyxxQ0FBcUMseUJBQXlCLHFEQUFxRCxHQUFHLGtDQUFrQyx5QkFBeUIsd0RBQXdELEdBQUcsaURBQWlELHVCQUF1Qiw4REFBOEQsR0FBRyxzREFBc0QseUJBQXlCLHVEQUF1RCxHQUFHLDZCQUE2QixvQkFBb0Isa0JBQWtCLEtBQUssc0JBQXNCLDBCQUEwQixLQUFLLGtCQUFrQiwyQkFBMkIsNEJBQTRCLEtBQUssR0FBRyw2QkFBNkIseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyxzQkFBc0IsMEJBQTBCLEtBQUssR0FBRyw4QkFBOEIsd0JBQXdCLG1DQUFtQyxLQUFLLGtCQUFrQixvQkFBb0IsS0FBSyxvQkFBb0Isb0JBQW9CLEtBQUssa0JBQWtCLHFCQUFxQixLQUFLLGtCQUFrQixtQkFBbUIsS0FBSyxpQkFBaUIscUJBQXFCLEtBQUssa0JBQWtCLG9CQUFvQixLQUFLLGlCQUFpQixvQkFBb0IsS0FBSyx1QkFBdUIsd0JBQXdCLHdCQUF3QixzTUFBc00sS0FBSyx5QkFBeUIsdURBQXVELEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyx5QkFBeUIsdURBQXVELEtBQUssa0JBQWtCLG9CQUFvQixLQUFLLGtCQUFrQixvQkFBb0IsS0FBSyxrQkFBa0IseUJBQXlCLDBCQUEwQixLQUFLLG1CQUFtQix3QkFBd0IsMkJBQTJCLEtBQUssc0JBQXNCLHdCQUF3Qix3QkFBd0IsS0FBSyxzQkFBc0IsMEJBQTBCLDJCQUEyQixLQUFLLHNCQUFzQixzQkFBc0IscUJBQXFCLEtBQUssc0JBQXNCLHlCQUF5QixxQkFBcUIsS0FBSyxHQUFHLDhCQUE4QixzQkFBc0IseUJBQXlCLHFCQUFxQixLQUFLLEdBQUcsa0dBQWtHLDRCQUE0QixHQUFHLFNBQVMsdUZBQXVGLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxVQUFVLFVBQVUsVUFBVSxVQUFVLFVBQVUsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxVQUFVLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsVUFBVSxVQUFVLFVBQVUsVUFBVSxVQUFVLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxVQUFVLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsS0FBSyxXQUFXLFlBQVksTUFBTSxPQUFPLHFCQUFxQixvQkFBb0IscUJBQXFCLHFCQUFxQixNQUFNLE1BQU0sV0FBVyxNQUFNLFlBQVksTUFBTSxNQUFNLHFCQUFxQixxQkFBcUIscUJBQXFCLFVBQVUsb0JBQW9CLHFCQUFxQixxQkFBcUIscUJBQXFCLHFCQUFxQixNQUFNLE9BQU8sTUFBTSxLQUFLLG9CQUFvQixxQkFBcUIsTUFBTSxRQUFRLE1BQU0sS0FBSyxvQkFBb0Isb0JBQW9CLHFCQUFxQixNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsV0FBVyxNQUFNLE1BQU0sTUFBTSxVQUFVLFdBQVcsV0FBVyxNQUFNLE1BQU0sTUFBTSxLQUFLLFVBQVUsV0FBVyxNQUFNLE1BQU0sTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE1BQU0sUUFBUSxxQkFBcUIscUJBQXFCLHFCQUFxQixvQkFBb0IsTUFBTSxNQUFNLE1BQU0sS0FBSyxVQUFVLE1BQU0sTUFBTSxNQUFNLE1BQU0sVUFBVSxVQUFVLFdBQVcsV0FBVyxNQUFNLEtBQUssVUFBVSxNQUFNLEtBQUssVUFBVSxNQUFNLFFBQVEsTUFBTSxLQUFLLG9CQUFvQixxQkFBcUIscUJBQXFCLE1BQU0sUUFBUSxNQUFNLFNBQVMscUJBQXFCLHFCQUFxQixxQkFBcUIsb0JBQW9CLHFCQUFxQixxQkFBcUIscUJBQXFCLG9CQUFvQixvQkFBb0Isb0JBQW9CLE1BQU0sTUFBTSxNQUFNLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxRQUFRLHFCQUFxQixxQkFBcUIscUJBQXFCLE1BQU0sTUFBTSxNQUFNLEtBQUssVUFBVSxNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsTUFBTSxNQUFNLE1BQU0sS0FBSyxXQUFXLE1BQU0sTUFBTSxNQUFNLE1BQU0sVUFBVSxNQUFNLE9BQU8sTUFBTSxLQUFLLHFCQUFxQixxQkFBcUIsTUFBTSxNQUFNLE1BQU0sS0FBSyxXQUFXLE1BQU0sT0FBTyxNQUFNLEtBQUsscUJBQXFCLG9CQUFvQixNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsTUFBTSxNQUFNLE1BQU0saUJBQWlCLFVBQVUsTUFBTSxLQUFLLFVBQVUsVUFBVSxNQUFNLEtBQUssVUFBVSxNQUFNLE9BQU8sV0FBVyxVQUFVLFVBQVUsTUFBTSxNQUFNLEtBQUssS0FBSyxVQUFVLE1BQU0sTUFBTSxNQUFNLEtBQUssV0FBVyxNQUFNLE9BQU8sTUFBTSxLQUFLLG9CQUFvQixvQkFBb0IsTUFBTSxNQUFNLG9CQUFvQixvQkFBb0IsTUFBTSxNQUFNLE1BQU0sTUFBTSxVQUFVLE1BQU0sTUFBTSxLQUFLLEtBQUssVUFBVSxNQUFNLFFBQVEsTUFBTSxZQUFZLG9CQUFvQixxQkFBcUIsTUFBTSxNQUFNLE1BQU0sTUFBTSxVQUFVLFVBQVUsTUFBTSxXQUFXLEtBQUssVUFBVSxNQUFNLEtBQUssV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxXQUFXLE1BQU0sS0FBSyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxLQUFLLE1BQU0sS0FBSyxVQUFVLEtBQUssTUFBTSxLQUFLLFVBQVUsS0FBSyxNQUFNLEtBQUssS0FBSyxNQUFNLEtBQUssV0FBVyxLQUFLLE1BQU0sS0FBSyxLQUFLLE1BQU0sS0FBSyxVQUFVLEtBQUssTUFBTSxLQUFLLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLEtBQUssTUFBTSxLQUFLLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLEtBQUssTUFBTSxLQUFLLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsVUFBVSxVQUFVLFVBQVUsV0FBVyxXQUFXLFdBQVcsS0FBSyxNQUFNLEtBQUssS0FBSyxNQUFNLEtBQUssS0FBSyxNQUFNLEtBQUssV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLEtBQUssTUFBTSxLQUFLLFdBQVcsV0FBVyxXQUFXLFdBQVcsS0FBSyxNQUFNLEtBQUssV0FBVyxNQUFNLE1BQU0sS0FBSyxXQUFXLFdBQVcsS0FBSyxLQUFLLE1BQU0sS0FBSyxXQUFXLE1BQU0sTUFBTSxLQUFLLFdBQVcsV0FBVyxLQUFLLEtBQUssTUFBTSxLQUFLLFdBQVcsS0FBSyxNQUFNLEtBQUssV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsTUFBTSxNQUFNLEtBQUssV0FBVyxXQUFXLEtBQUssS0FBSyxNQUFNLEtBQUssV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLFdBQVcsVUFBVSxXQUFXLFVBQVUsV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLEtBQUssS0FBSyxLQUFLLFdBQVcsTUFBTSxLQUFLLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxLQUFLLE1BQU0sV0FBVyxLQUFLLFdBQVcsV0FBVyxXQUFXLE1BQU0sS0FBSyxXQUFXLFdBQVcsTUFBTSxLQUFLLFdBQVcsV0FBVyxNQUFNLFdBQVcsS0FBSyxXQUFXLE1BQU0sV0FBVyxLQUFLLFVBQVUsTUFBTSxLQUFLLFdBQVcsS0FBSyxNQUFNLEtBQUssV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLFdBQVcsS0FBSyxNQUFNLEtBQUssV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLEtBQUssTUFBTSxLQUFLLFdBQVcsS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNLE9BQU8sS0FBSyxLQUFLLE1BQU0sS0FBSyxPQUFPLEtBQUssS0FBSyxNQUFNLEtBQUssT0FBTyxLQUFLLEtBQUssTUFBTSxLQUFLLE9BQU8sS0FBSyxLQUFLLE1BQU0sS0FBSyxPQUFPLEtBQUssS0FBSyxNQUFNLEtBQUssTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxjQUFjLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLE1BQU0sS0FBSyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxLQUFLLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLEtBQUssWUFBWSxNQUFNLFlBQVksY0FBYyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFdBQVcsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sS0FBSyxVQUFVLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxZQUFZLGFBQWEsTUFBTSxLQUFLLFVBQVUsS0FBSyxLQUFLLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxZQUFZLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLEtBQUssWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLFlBQVksT0FBTyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sWUFBWSxhQUFhLE1BQU0sWUFBWSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssTUFBTSxZQUFZLE1BQU0sTUFBTSxPQUFPLEtBQUssWUFBWSxNQUFNLE1BQU0sS0FBSyxPQUFPLEtBQUssWUFBWSxNQUFNLE1BQU0sS0FBSyxhQUFhLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLEtBQUssWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssTUFBTSxZQUFZLGFBQWEsYUFBYSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE9BQU8sS0FBSyxZQUFZLE1BQU0sTUFBTSxLQUFLLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxhQUFhLE1BQU0sZUFBZSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLFlBQVksTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLEtBQUssYUFBYSxhQUFhLGFBQWEsY0FBYyxjQUFjLGFBQWEsYUFBYSxhQUFhLE1BQU0sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsU0FBUyxPQUFPLFdBQVcsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sS0FBSyxVQUFVLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxZQUFZLGFBQWEsTUFBTSxLQUFLLFVBQVUsS0FBSyxLQUFLLFlBQVksU0FBUyxPQUFPLGFBQWEsYUFBYSxhQUFhLE1BQU0sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sS0FBSyxVQUFVLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxZQUFZLGFBQWEsYUFBYSxNQUFNLEtBQUssWUFBWSxhQUFhLGFBQWEsTUFBTSxZQUFZLFFBQVEsS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxXQUFXLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxPQUFPLEtBQUssS0FBSyxNQUFNLEtBQUssTUFBTSxLQUFLLE1BQU0sT0FBTyxLQUFLLEtBQUssTUFBTSxLQUFLLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxXQUFXLE1BQU0sTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxlQUFlLE1BQU0sWUFBWSxNQUFNLFlBQVksY0FBYyxnQkFBZ0IsYUFBYSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGNBQWMsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxPQUFPLEtBQUssWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsT0FBTyxhQUFhLGVBQWUsZ0JBQWdCLGNBQWMsTUFBTSxLQUFLLFVBQVUsWUFBWSxNQUFNLEtBQUssVUFBVSxZQUFZLE1BQU0sTUFBTSxLQUFLLEtBQUssVUFBVSxZQUFZLE1BQU0sS0FBSyxVQUFVLFlBQVksTUFBTSxNQUFNLEtBQUssS0FBSyxVQUFVLFlBQVksTUFBTSxLQUFLLFVBQVUsWUFBWSxNQUFNLE1BQU0sS0FBSyxLQUFLLFVBQVUsWUFBWSxNQUFNLEtBQUssVUFBVSxZQUFZLE1BQU0sTUFBTSxZQUFZLE1BQU0sc0JBQXNCLHVCQUF1QixPQUFPLEtBQUssc0JBQXNCLHVCQUF1Qix1QkFBdUIsdUJBQXVCLE9BQU8sS0FBSyxzQkFBc0IsdUJBQXVCLE9BQU8sS0FBSyxLQUFLLFlBQVksYUFBYSxNQUFNLEtBQUssWUFBWSxhQUFhLE1BQU0sS0FBSyxZQUFZLGFBQWEsTUFBTSxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sS0FBSyxLQUFLLFlBQVksYUFBYSxhQUFhLE1BQU0sS0FBSyxZQUFZLGFBQWEsYUFBYSxNQUFNLE1BQU0sWUFBWSxNQUFNLHNCQUFzQix1QkFBdUIsT0FBTyxLQUFLLHNCQUFzQix1QkFBdUIsdUJBQXVCLE9BQU8sWUFBWSxNQUFNLEtBQUssWUFBWSxhQUFhLE1BQU0sS0FBSyxZQUFZLGFBQWEsTUFBTSxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sS0FBSyxzQkFBc0IsdUJBQXVCLE9BQU8sS0FBSyxLQUFLLFlBQVksV0FBVyxLQUFLLEtBQUssWUFBWSxXQUFXLEtBQUssS0FBSyxZQUFZLFdBQVcsS0FBSyxRQUFRLEtBQUssS0FBSyxZQUFZLFdBQVcsS0FBSyxLQUFLLFlBQVksV0FBVyxLQUFLLE1BQU0sWUFBWSxNQUFNLE9BQU8sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sWUFBWSxNQUFNLEtBQUssWUFBWSxPQUFPLE9BQU8sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLEtBQUssT0FBTyxhQUFhLGVBQWUsT0FBTyxPQUFPLE9BQU8sYUFBYSxPQUFPLE9BQU8sT0FBTyxLQUFLLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sYUFBYSxPQUFPLE9BQU8sT0FBTyxLQUFLLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxPQUFPLGFBQWEsZUFBZSxPQUFPLE9BQU8sT0FBTyxLQUFLLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sYUFBYSxlQUFlLE9BQU8sT0FBTyxPQUFPLGFBQWEsZUFBZSxPQUFPLE9BQU8sT0FBTyxZQUFZLE9BQU8sT0FBTyxPQUFPLEtBQUssT0FBTyxPQUFPLEtBQUssT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sYUFBYSxPQUFPLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sS0FBSyxPQUFPLE9BQU8sS0FBSyxPQUFPLE9BQU8sS0FBSyxPQUFPLE9BQU8sYUFBYSxlQUFlLE9BQU8sT0FBTyxPQUFPLEtBQUssT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sYUFBYSxPQUFPLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxPQUFPLGFBQWEsT0FBTyxPQUFPLE9BQU8sYUFBYSxPQUFPLE9BQU8sT0FBTyxhQUFhLE9BQU8sT0FBTyxRQUFRLEtBQUssS0FBSyxRQUFRLE9BQU8sS0FBSyxRQUFRLE9BQU8sYUFBYSxPQUFPLE9BQU8sS0FBSyxRQUFRLEtBQUssS0FBSyxRQUFRLE9BQU8sS0FBSyxRQUFRLE9BQU8sS0FBSyxPQUFPLEtBQUssUUFBUSxLQUFLLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLEtBQUssUUFBUSxPQUFPLGFBQWEsZUFBZSxPQUFPLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxLQUFLLFFBQVEsT0FBTyxhQUFhLE9BQU8sUUFBUSxPQUFPLGFBQWEsT0FBTyxRQUFRLE9BQU8sYUFBYSxPQUFPLFFBQVEsT0FBTyxhQUFhLE9BQU8sUUFBUSxPQUFPLFlBQVksT0FBTyxRQUFRLE9BQU8sYUFBYSxPQUFPLE9BQU8sS0FBSyxRQUFRLEtBQUssYUFBYSxPQUFPLE9BQU8sS0FBSyxPQUFPLEtBQUssMENBQTBDLHVCQUF1QixzQkFBc0Isb0VBQW9FLG9FQUFvRSx5RkFBeUYsNEJBQTRCLDRCQUE0Qiw0QkFBNEIsOEJBQThCLHlDQUF5Qyw2Q0FBNkMsd0JBQXdCLDJCQUEyQiwrR0FBK0csOENBQThDLGtEQUFrRCw2REFBNkQsbUNBQW1DLGtDQUFrQyxxREFBcUQsaUNBQWlDLGlEQUFpRCxpTUFBaU0sNEZBQTRGLCtEQUErRCxHQUFHLDRCQUE0QiwwQkFBMEIsNEJBQTRCLDRCQUE0Qiw0QkFBNEIsOEJBQThCLHlDQUF5Qyx1Q0FBdUMsd0JBQXdCLDJCQUEyQixHQUFHLG9DQUFvQyxVQUFVLDhCQUE4QixLQUFLLGNBQWMsc0VBQXNFLG9DQUFvQyxpQ0FBaUMsS0FBSyxpRUFBaUUsd0ZBQXdGLDBEQUEwRCx5Q0FBeUMsS0FBSyxtQkFBbUIsb0dBQW9HLDRFQUE0RSxLQUFLLHFCQUFxQixzR0FBc0csZ0VBQWdFLEtBQUssb0RBQW9ELHdCQUF3QixLQUFLLG9DQUFvQyxxQkFBcUIsdUJBQXVCLEtBQUssdURBQXVELGlCQUFpQixLQUFLLG1DQUFtQywwQ0FBMEMsS0FBSyxtQ0FBbUMsdURBQXVELEtBQUsseUNBQXlDLDBDQUEwQyxLQUFLLEdBQUcsK0NBQStDLHVFQUF1RSw2UEFBNlAsMENBQTBDLGtDQUFrQyxpREFBaUQsS0FBSyxzQkFBc0IsdVBBQXVQLDBDQUEwQyxrQ0FBa0MsS0FBSyxvQkFBb0IsaVBBQWlQLDBDQUEwQyxrQ0FBa0MsS0FBSyx5REFBeUQsb0tBQW9LLDBDQUEwQyxrQ0FBa0MsaURBQWlELGdEQUFnRCxLQUFLLHdCQUF3QixrREFBa0QsS0FBSyxrRkFBa0Ysd0dBQXdHLDBDQUEwQyxrQ0FBa0Msa0RBQWtELDBCQUEwQiw4QkFBOEIsMEJBQTBCLHVCQUF1Qix3REFBd0QseUJBQXlCLHVCQUF1QixLQUFLLHNDQUFzQyxrQkFBa0IseUJBQXlCLGFBQWEsa0JBQWtCLGtCQUFrQixtQkFBbUIseUdBQXlHLGlDQUFpQyxLQUFLLDRDQUE0QyxpQkFBaUIsS0FBSyxvQ0FBb0MsOENBQThDLHdHQUF3Ryw2Q0FBNkMsMkZBQTJGLEtBQUssMEVBQTBFLHdHQUF3RywwQ0FBMEMsa0NBQWtDLG1EQUFtRCwwQkFBMEIseUJBQXlCLHNCQUFzQix1QkFBdUIsd0RBQXdELHlCQUF5Qix1QkFBdUIsZ0RBQWdELEtBQUssNkJBQTZCLGtCQUFrQix5QkFBeUIsYUFBYSxrQkFBa0Isa0JBQWtCLG1CQUFtQiwwR0FBMEcsaUNBQWlDLEtBQUssbUNBQW1DLGlCQUFpQixLQUFLLDJCQUEyQiw4Q0FBOEMsd0dBQXdHLDZDQUE2Qyw0RkFBNEYsS0FBSyx3RUFBd0Usd0dBQXdHLDBDQUEwQyxrQ0FBa0Msa0RBQWtELDBCQUEwQixnREFBZ0Qsd0RBQXdELEtBQUsseUJBQXlCLHdHQUF3Ryw4Q0FBOEMsa0RBQWtELDhDQUE4QyxLQUFLLGdFQUFnRSw4SUFBOEksMENBQTBDLGtDQUFrQyxLQUFLLG1CQUFtQix3R0FBd0csS0FBSyxtQkFBbUIscURBQXFELDRDQUE0QywwQ0FBMEMsa0NBQWtDLGtEQUFrRCxnREFBZ0QsS0FBSyx5QkFBeUIsNENBQTRDLDhDQUE4QyxrREFBa0QsS0FBSyxxQkFBcUIscURBQXFELHVHQUF1RywwQ0FBMEMsa0NBQWtDLGlEQUFpRCx5RkFBeUYsS0FBSywyQkFBMkIsOENBQThDLDBGQUEwRixLQUFLLDRDQUE0Qyx5T0FBeU8sS0FBSyxtREFBbUQsNEJBQTRCLEtBQUssNEJBQTRCLGtDQUFrQyxLQUFLLGdHQUFnRywyQ0FBMkMsdUJBQXVCLHdCQUF3Qiw4QkFBOEIsd0JBQXdCLGlGQUFpRiw0QkFBNEIsb0NBQW9DLDJDQUEyQyxpREFBaUQsd0RBQXdELEtBQUssNEJBQTRCLDJDQUEyQyx1QkFBdUIsdUJBQXVCLDhCQUE4Qix3QkFBd0IsS0FBSyxtQkFBbUIsb0ZBQW9GLEtBQUssbUJBQW1CLHdFQUF3RSxLQUFLLG1CQUFtQiwrREFBK0QsS0FBSyxtQkFBbUIsK0NBQStDLEtBQUssOERBQThELDhHQUE4RyxpQ0FBaUMsdURBQXVELEtBQUssOEJBQThCLDhGQUE4RixpQ0FBaUMsb0NBQW9DLDRCQUE0QiwyQ0FBMkMseURBQXlELGdEQUFnRCxLQUFLLHVFQUF1RSwyR0FBMkcsaUNBQWlDLG9DQUFvQyw0QkFBNEIsMkNBQTJDLG1EQUFtRCxxREFBcUQsNERBQTRELEtBQUssbUNBQW1DLHFRQUFxUSxpQ0FBaUMsb0NBQW9DLDRCQUE0QiwyQ0FBMkMsMERBQTBELHFEQUFxRCx5SkFBeUoseUJBQXlCLEtBQUssb0dBQW9HLHFRQUFxUSxpQ0FBaUMsb0NBQW9DLDRCQUE0QiwyQ0FBMkMsK0RBQStELG1HQUFtRyw0REFBNEQseUJBQXlCLDBGQUEwRiwwQ0FBMEMseUNBQXlDLG9EQUFvRCwrQkFBK0IsdUNBQXVDLGtDQUFrQyxLQUFLLHVCQUF1QixzQ0FBc0MsZ0RBQWdELEtBQUssd0RBQXdELHdCQUF3Qix3RkFBd0YsMERBQTBELHlDQUF5QyxLQUFLLDRCQUE0Qix3QkFBd0IsZ0VBQWdFLHVCQUF1QixLQUFLLDRCQUE0Qix3QkFBd0Isd0ZBQXdGLDBEQUEwRCx5Q0FBeUMsaUNBQWlDLG9EQUFvRCw4QkFBOEIsdUJBQXVCLHVCQUF1QixLQUFLLDhGQUE4Riw4RkFBOEYsb0NBQW9DLDRCQUE0QiwyQ0FBMkMsZ0RBQWdELHdEQUF3RCxLQUFLLDJEQUEyRCxvRUFBb0UsNEJBQTRCLG1EQUFtRCxLQUFLLGlFQUFpRSx3R0FBd0csMENBQTBDLGtDQUFrQyxrREFBa0QsMEJBQTBCLGdEQUFnRCx1Q0FBdUMsS0FBSyxpQ0FBaUMsOENBQThDLGtEQUFrRCx3R0FBd0csS0FBSyx5REFBeUQsb0VBQW9FLDBDQUEwQyxrQ0FBa0MsaURBQWlELG9EQUFvRCxtQkFBbUIsZ0RBQWdELHVDQUF1Qyx5QkFBeUIsdUJBQXVCLEtBQUssZ0NBQWdDLDhDQUE4QyxxREFBcUQsS0FBSyxxRUFBcUUseUdBQXlHLHdEQUF3RCxLQUFLLHFEQUFxRCxvRUFBb0UsaURBQWlELG9EQUFvRCwwQkFBMEIsS0FBSyx1RUFBdUUsdUdBQXVHLDBDQUEwQyxrQ0FBa0MsaURBQWlELGdEQUFnRCwwQkFBMEIsS0FBSyxnRUFBZ0UsME1BQTBNLGlDQUFpQywwQ0FBMEMsa0NBQWtDLCtDQUErQyxvTUFBb00scUJBQXFCLHVCQUF1QixzREFBc0QsMEJBQTBCLHlCQUF5Qix1Q0FBdUMseUJBQXlCLHVCQUF1QixtREFBbUQsS0FBSyxpQ0FBaUMsa0JBQWtCLHlCQUF5QixhQUFhLGtCQUFrQixrQkFBa0IsbUJBQW1CLHlHQUF5RyxpQ0FBaUMsS0FBSyx1Q0FBdUMsaUJBQWlCLEtBQUssK0JBQStCLDhDQUE4QyxxTUFBcU0scUNBQXFDLDRDQUE0QywyQ0FBMkMsS0FBSywwREFBMEQsbUdBQW1HLDBDQUEwQyxrQ0FBa0MsK0NBQStDLDBCQUEwQiw0RkFBNEYsdUNBQXVDLHlCQUF5Qix1QkFBdUIsS0FBSyxtQ0FBbUMsa0JBQWtCLHlCQUF5QixnQkFBZ0IsaUJBQWlCLGtCQUFrQixtQkFBbUIsc0ZBQXNGLGtEQUFrRCwyQkFBMkIsS0FBSyxpQ0FBaUMsOENBQThDLDZGQUE2RiwyQ0FBMkMsS0FBSywwREFBMEQsaURBQWlELEtBQUssK0JBQStCLG1EQUFtRCxLQUFLLCtCQUErQixtREFBbUQsS0FBSyxnQ0FBZ0Msb0RBQW9ELEtBQUssR0FBRyw2Q0FBNkMsbURBQW1ELHdCQUF3QixLQUFLLG1CQUFtQixzQkFBc0IsS0FBSyxvREFBb0QsZ0NBQWdDLEtBQUssa0JBQWtCLDhCQUE4QixLQUFLLGtEQUFrRCxpQ0FBaUMsS0FBSyxlQUFlLCtCQUErQixLQUFLLEdBQUcsb0RBQW9ELFVBQVUsaUJBQWlCLGtDQUFrQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcsMkJBQTJCLFVBQVUsaUJBQWlCLG1DQUFtQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcsMkJBQTJCLFVBQVUsaUJBQWlCLG1DQUFtQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcsNEJBQTRCLFVBQVUsaUJBQWlCLGtDQUFrQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcsb0ZBQW9GLGVBQWUsOEJBQThCLFVBQVUsZ0NBQWdDLEdBQUcsZ0NBQWdDLGVBQWUsNkJBQTZCLFVBQVUsK0JBQStCLFVBQVUsaUNBQWlDLFVBQVUsK0JBQStCLEdBQUcsNkJBQTZCLFNBQVMsK0JBQStCLFdBQVcsOEJBQThCLEdBQUcsMkJBQTJCLGNBQWMsOENBQThDLG9DQUFvQyxTQUFTLGdEQUFnRCxvQ0FBb0MsU0FBUyxnREFBZ0Qsb0NBQW9DLFNBQVMsaURBQWlELG9DQUFvQyxHQUFHLDJCQUEyQixjQUFjLDJDQUEyQyw2Q0FBNkMsZ0RBQWdELEtBQUssU0FBUyw0Q0FBNEMsNkNBQTZDLGtEQUFrRCxLQUFLLEdBQUcsa0VBQWtFLGVBQWUsOEJBQThCLFVBQVUsZ0NBQWdDLEdBQUcsaUNBQWlDLFNBQVMsZ0NBQWdDLFVBQVUsNkJBQTZCLFdBQVcsK0JBQStCLEdBQUcsc0ZBQXNGLFFBQVEsb0NBQW9DLCtCQUErQixLQUFLLFNBQVMsaUNBQWlDLCtCQUErQixLQUFLLFVBQVUsbUNBQW1DLCtCQUErQixLQUFLLEdBQUcsNEJBQTRCLFNBQVMsNkNBQTZDLFdBQVcsNENBQTRDLEdBQUcsNEJBQTRCLFFBQVEsbUNBQW1DLGlCQUFpQixLQUFLLFNBQVMsZ0NBQWdDLGlCQUFpQixLQUFLLFVBQVUsa0NBQWtDLGlCQUFpQixLQUFLLEdBQUcsNkJBQTZCLGNBQWMsdUNBQXVDLG1CQUFtQixLQUFLLFNBQVMsMkNBQTJDLG1CQUFtQixLQUFLLEdBQUcsK0VBQStFLGtDQUFrQyw0Q0FBNEMsOENBQThDLDZDQUE2Qyx1Q0FBdUMsS0FBSyxHQUFHLHNDQUFzQyxlQUFlLCtCQUErQixLQUFLLGdEQUFnRCxtQ0FBbUMsdUNBQXVDLCtDQUErQyx3Q0FBd0MsS0FBSyxHQUFHLHFCQUFxQjtBQUN2dHNIO0FBQ0EsK0RBQWUsdUJBQXVCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3N0eWxlcy9nbG9iYWxzLmNzcz8zMzFmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydHNcbmltcG9ydCBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18gZnJvbSBcIi4uLy4uLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL3J1bnRpbWUvYXBpLmpzXCI7XG5pbXBvcnQgX19fQ1NTX0xPQURFUl9BVF9SVUxFX0lNUE9SVF8wX19fIGZyb20gXCItIS4uLy4uLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzEzXS51c2VbMV0hLi4vLi4vLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzEzXS51c2VbMl0hLi90aGVtZXMvdGhlbWUtYmFzZS5jc3NcIjtcbnZhciBfX19DU1NfTE9BREVSX0VYUE9SVF9fXyA9IF9fX0NTU19MT0FERVJfQVBJX0lNUE9SVF9fXyh0cnVlKTtcbl9fX0NTU19MT0FERVJfRVhQT1JUX19fLmkoX19fQ1NTX0xPQURFUl9BVF9SVUxFX0lNUE9SVF8wX19fKTtcbi8vIE1vZHVsZVxuX19fQ1NTX0xPQURFUl9FWFBPUlRfX18ucHVzaChbbW9kdWxlLmlkLCBcIiosIDo6YmVmb3JlLCA6OmFmdGVyIHtcXG4gIC0tdHctYm9yZGVyLXNwYWNpbmcteDogMDtcXG4gIC0tdHctYm9yZGVyLXNwYWNpbmcteTogMDtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IDA7XFxuICAtLXR3LXRyYW5zbGF0ZS15OiAwO1xcbiAgLS10dy1yb3RhdGU6IDA7XFxuICAtLXR3LXNrZXcteDogMDtcXG4gIC0tdHctc2tldy15OiAwO1xcbiAgLS10dy1zY2FsZS14OiAxO1xcbiAgLS10dy1zY2FsZS15OiAxO1xcbiAgLS10dy1wYW4teDogIDtcXG4gIC0tdHctcGFuLXk6ICA7XFxuICAtLXR3LXBpbmNoLXpvb206ICA7XFxuICAtLXR3LXNjcm9sbC1zbmFwLXN0cmljdG5lc3M6IHByb3hpbWl0eTtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbjogIDtcXG4gIC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1ncmFkaWVudC10by1wb3NpdGlvbjogIDtcXG4gIC0tdHctb3JkaW5hbDogIDtcXG4gIC0tdHctc2xhc2hlZC16ZXJvOiAgO1xcbiAgLS10dy1udW1lcmljLWZpZ3VyZTogIDtcXG4gIC0tdHctbnVtZXJpYy1zcGFjaW5nOiAgO1xcbiAgLS10dy1udW1lcmljLWZyYWN0aW9uOiAgO1xcbiAgLS10dy1yaW5nLWluc2V0OiAgO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMHB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZmZjtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDU5IDEzMCAyNDYgLyAwLjUpO1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctcmluZy1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDAgIzAwMDA7XFxuICAtLXR3LWJsdXI6ICA7XFxuICAtLXR3LWJyaWdodG5lc3M6ICA7XFxuICAtLXR3LWNvbnRyYXN0OiAgO1xcbiAgLS10dy1ncmF5c2NhbGU6ICA7XFxuICAtLXR3LWh1ZS1yb3RhdGU6ICA7XFxuICAtLXR3LWludmVydDogIDtcXG4gIC0tdHctc2F0dXJhdGU6ICA7XFxuICAtLXR3LXNlcGlhOiAgO1xcbiAgLS10dy1kcm9wLXNoYWRvdzogIDtcXG4gIC0tdHctYmFja2Ryb3AtYmx1cjogIDtcXG4gIC0tdHctYmFja2Ryb3AtYnJpZ2h0bmVzczogIDtcXG4gIC0tdHctYmFja2Ryb3AtY29udHJhc3Q6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWdyYXlzY2FsZTogIDtcXG4gIC0tdHctYmFja2Ryb3AtaHVlLXJvdGF0ZTogIDtcXG4gIC0tdHctYmFja2Ryb3AtaW52ZXJ0OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1vcGFjaXR5OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1zYXR1cmF0ZTogIDtcXG4gIC0tdHctYmFja2Ryb3Atc2VwaWE6ICA7XFxuICAtLXR3LWNvbnRhaW4tc2l6ZTogIDtcXG4gIC0tdHctY29udGFpbi1sYXlvdXQ6ICA7XFxuICAtLXR3LWNvbnRhaW4tcGFpbnQ6ICA7XFxuICAtLXR3LWNvbnRhaW4tc3R5bGU6ICA7XFxufVxcblxcbjo6YmFja2Ryb3Age1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy14OiAwO1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy15OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteDogMDtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IDA7XFxuICAtLXR3LXJvdGF0ZTogMDtcXG4gIC0tdHctc2tldy14OiAwO1xcbiAgLS10dy1za2V3LXk6IDA7XFxuICAtLXR3LXNjYWxlLXg6IDE7XFxuICAtLXR3LXNjYWxlLXk6IDE7XFxuICAtLXR3LXBhbi14OiAgO1xcbiAgLS10dy1wYW4teTogIDtcXG4gIC0tdHctcGluY2gtem9vbTogIDtcXG4gIC0tdHctc2Nyb2xsLXNuYXAtc3RyaWN0bmVzczogcHJveGltaXR5O1xcbiAgLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1ncmFkaWVudC12aWEtcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1vcmRpbmFsOiAgO1xcbiAgLS10dy1zbGFzaGVkLXplcm86ICA7XFxuICAtLXR3LW51bWVyaWMtZmlndXJlOiAgO1xcbiAgLS10dy1udW1lcmljLXNwYWNpbmc6ICA7XFxuICAtLXR3LW51bWVyaWMtZnJhY3Rpb246ICA7XFxuICAtLXR3LXJpbmctaW5zZXQ6ICA7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAwcHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmZmO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTkgMTMwIDI0NiAvIDAuNSk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMCAjMDAwMDtcXG4gIC0tdHctYmx1cjogIDtcXG4gIC0tdHctYnJpZ2h0bmVzczogIDtcXG4gIC0tdHctY29udHJhc3Q6ICA7XFxuICAtLXR3LWdyYXlzY2FsZTogIDtcXG4gIC0tdHctaHVlLXJvdGF0ZTogIDtcXG4gIC0tdHctaW52ZXJ0OiAgO1xcbiAgLS10dy1zYXR1cmF0ZTogIDtcXG4gIC0tdHctc2VwaWE6ICA7XFxuICAtLXR3LWRyb3Atc2hhZG93OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ibHVyOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1jb250cmFzdDogIDtcXG4gIC0tdHctYmFja2Ryb3AtZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1pbnZlcnQ6ICA7XFxuICAtLXR3LWJhY2tkcm9wLW9wYWNpdHk6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNhdHVyYXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1zZXBpYTogIDtcXG4gIC0tdHctY29udGFpbi1zaXplOiAgO1xcbiAgLS10dy1jb250YWluLWxheW91dDogIDtcXG4gIC0tdHctY29udGFpbi1wYWludDogIDtcXG4gIC0tdHctY29udGFpbi1zdHlsZTogIDtcXG59LypcXG4hIHRhaWx3aW5kY3NzIHYzLjQuMTcgfCBNSVQgTGljZW5zZSB8IGh0dHBzOi8vdGFpbHdpbmRjc3MuY29tXFxuKi8vKlxcbjEuIFByZXZlbnQgcGFkZGluZyBhbmQgYm9yZGVyIGZyb20gYWZmZWN0aW5nIGVsZW1lbnQgd2lkdGguIChodHRwczovL2dpdGh1Yi5jb20vbW96ZGV2cy9jc3NyZW1lZHkvaXNzdWVzLzQpXFxuMi4gQWxsb3cgYWRkaW5nIGEgYm9yZGVyIHRvIGFuIGVsZW1lbnQgYnkganVzdCBhZGRpbmcgYSBib3JkZXItd2lkdGguIChodHRwczovL2dpdGh1Yi5jb20vdGFpbHdpbmRjc3MvdGFpbHdpbmRjc3MvcHVsbC8xMTYpXFxuKi9cXG5cXG4qLFxcbjo6YmVmb3JlLFxcbjo6YWZ0ZXIge1xcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDsgLyogMSAqL1xcbiAgYm9yZGVyLXdpZHRoOiAwOyAvKiAyICovXFxuICBib3JkZXItc3R5bGU6IHNvbGlkOyAvKiAyICovXFxuICBib3JkZXItY29sb3I6ICNlNWU3ZWI7IC8qIDIgKi9cXG59XFxuXFxuOjpiZWZvcmUsXFxuOjphZnRlciB7XFxuICAtLXR3LWNvbnRlbnQ6ICcnO1xcbn1cXG5cXG4vKlxcbjEuIFVzZSBhIGNvbnNpc3RlbnQgc2Vuc2libGUgbGluZS1oZWlnaHQgaW4gYWxsIGJyb3dzZXJzLlxcbjIuIFByZXZlbnQgYWRqdXN0bWVudHMgb2YgZm9udCBzaXplIGFmdGVyIG9yaWVudGF0aW9uIGNoYW5nZXMgaW4gaU9TLlxcbjMuIFVzZSBhIG1vcmUgcmVhZGFibGUgdGFiIHNpemUuXFxuNC4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgc2Fuc2AgZm9udC1mYW1pbHkgYnkgZGVmYXVsdC5cXG41LiBVc2UgdGhlIHVzZXIncyBjb25maWd1cmVkIGBzYW5zYCBmb250LWZlYXR1cmUtc2V0dGluZ3MgYnkgZGVmYXVsdC5cXG42LiBVc2UgdGhlIHVzZXIncyBjb25maWd1cmVkIGBzYW5zYCBmb250LXZhcmlhdGlvbi1zZXR0aW5ncyBieSBkZWZhdWx0LlxcbjcuIERpc2FibGUgdGFwIGhpZ2hsaWdodHMgb24gaU9TXFxuKi9cXG5cXG5odG1sLFxcbjpob3N0IHtcXG4gIGxpbmUtaGVpZ2h0OiAxLjU7IC8qIDEgKi9cXG4gIC13ZWJraXQtdGV4dC1zaXplLWFkanVzdDogMTAwJTsgLyogMiAqL1xcbiAgLW1vei10YWItc2l6ZTogNDsgLyogMyAqL1xcbiAgLW8tdGFiLXNpemU6IDQ7XFxuICAgICB0YWItc2l6ZTogNDsgLyogMyAqL1xcbiAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtaW50ZXIpLCBJbnRlciwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmOyAvKiA0ICovXFxuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IG5vcm1hbDsgLyogNSAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IG5vcm1hbDsgLyogNiAqL1xcbiAgLXdlYmtpdC10YXAtaGlnaGxpZ2h0LWNvbG9yOiB0cmFuc3BhcmVudDsgLyogNyAqL1xcbn1cXG5cXG4vKlxcbjEuIFJlbW92ZSB0aGUgbWFyZ2luIGluIGFsbCBicm93c2Vycy5cXG4yLiBJbmhlcml0IGxpbmUtaGVpZ2h0IGZyb20gYGh0bWxgIHNvIHVzZXJzIGNhbiBzZXQgdGhlbSBhcyBhIGNsYXNzIGRpcmVjdGx5IG9uIHRoZSBgaHRtbGAgZWxlbWVudC5cXG4qL1xcblxcbmJvZHkge1xcbiAgbWFyZ2luOiAwOyAvKiAxICovXFxuICBsaW5lLWhlaWdodDogaW5oZXJpdDsgLyogMiAqL1xcbn1cXG5cXG4vKlxcbjEuIEFkZCB0aGUgY29ycmVjdCBoZWlnaHQgaW4gRmlyZWZveC5cXG4yLiBDb3JyZWN0IHRoZSBpbmhlcml0YW5jZSBvZiBib3JkZXIgY29sb3IgaW4gRmlyZWZveC4gKGh0dHBzOi8vYnVnemlsbGEubW96aWxsYS5vcmcvc2hvd19idWcuY2dpP2lkPTE5MDY1NSlcXG4zLiBFbnN1cmUgaG9yaXpvbnRhbCBydWxlcyBhcmUgdmlzaWJsZSBieSBkZWZhdWx0LlxcbiovXFxuXFxuaHIge1xcbiAgaGVpZ2h0OiAwOyAvKiAxICovXFxuICBjb2xvcjogaW5oZXJpdDsgLyogMiAqL1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4OyAvKiAzICovXFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IHRleHQgZGVjb3JhdGlvbiBpbiBDaHJvbWUsIEVkZ2UsIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5hYmJyOndoZXJlKFt0aXRsZV0pIHtcXG4gIC13ZWJraXQtdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmUgZG90dGVkO1xcbiAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZSBkb3R0ZWQ7XFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBkZWZhdWx0IGZvbnQgc2l6ZSBhbmQgd2VpZ2h0IGZvciBoZWFkaW5ncy5cXG4qL1xcblxcbmgxLFxcbmgyLFxcbmgzLFxcbmg0LFxcbmg1LFxcbmg2IHtcXG4gIGZvbnQtc2l6ZTogaW5oZXJpdDtcXG4gIGZvbnQtd2VpZ2h0OiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcblJlc2V0IGxpbmtzIHRvIG9wdGltaXplIGZvciBvcHQtaW4gc3R5bGluZyBpbnN0ZWFkIG9mIG9wdC1vdXQuXFxuKi9cXG5cXG5hIHtcXG4gIGNvbG9yOiBpbmhlcml0O1xcbiAgdGV4dC1kZWNvcmF0aW9uOiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBmb250IHdlaWdodCBpbiBFZGdlIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5iLFxcbnN0cm9uZyB7XFxuICBmb250LXdlaWdodDogYm9sZGVyO1xcbn1cXG5cXG4vKlxcbjEuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYG1vbm9gIGZvbnQtZmFtaWx5IGJ5IGRlZmF1bHQuXFxuMi4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC1mZWF0dXJlLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuMy4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC12YXJpYXRpb24tc2V0dGluZ3MgYnkgZGVmYXVsdC5cXG40LiBDb3JyZWN0IHRoZSBvZGQgYGVtYCBmb250IHNpemluZyBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5jb2RlLFxcbmtiZCxcXG5zYW1wLFxcbnByZSB7XFxuICBmb250LWZhbWlseTogSmV0QnJhaW5zIE1vbm8sIG1vbm9zcGFjZTsgLyogMSAqL1xcbiAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBub3JtYWw7IC8qIDIgKi9cXG4gIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzOiBub3JtYWw7IC8qIDMgKi9cXG4gIGZvbnQtc2l6ZTogMWVtOyAvKiA0ICovXFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IGZvbnQgc2l6ZSBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5zbWFsbCB7XFxuICBmb250LXNpemU6IDgwJTtcXG59XFxuXFxuLypcXG5QcmV2ZW50IGBzdWJgIGFuZCBgc3VwYCBlbGVtZW50cyBmcm9tIGFmZmVjdGluZyB0aGUgbGluZSBoZWlnaHQgaW4gYWxsIGJyb3dzZXJzLlxcbiovXFxuXFxuc3ViLFxcbnN1cCB7XFxuICBmb250LXNpemU6IDc1JTtcXG4gIGxpbmUtaGVpZ2h0OiAwO1xcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcbiAgdmVydGljYWwtYWxpZ246IGJhc2VsaW5lO1xcbn1cXG5cXG5zdWIge1xcbiAgYm90dG9tOiAtMC4yNWVtO1xcbn1cXG5cXG5zdXAge1xcbiAgdG9wOiAtMC41ZW07XFxufVxcblxcbi8qXFxuMS4gUmVtb3ZlIHRleHQgaW5kZW50YXRpb24gZnJvbSB0YWJsZSBjb250ZW50cyBpbiBDaHJvbWUgYW5kIFNhZmFyaS4gKGh0dHBzOi8vYnVncy5jaHJvbWl1bS5vcmcvcC9jaHJvbWl1bS9pc3N1ZXMvZGV0YWlsP2lkPTk5OTA4OCwgaHR0cHM6Ly9idWdzLndlYmtpdC5vcmcvc2hvd19idWcuY2dpP2lkPTIwMTI5NylcXG4yLiBDb3JyZWN0IHRhYmxlIGJvcmRlciBjb2xvciBpbmhlcml0YW5jZSBpbiBhbGwgQ2hyb21lIGFuZCBTYWZhcmkuIChodHRwczovL2J1Z3MuY2hyb21pdW0ub3JnL3AvY2hyb21pdW0vaXNzdWVzL2RldGFpbD9pZD05MzU3MjksIGh0dHBzOi8vYnVncy53ZWJraXQub3JnL3Nob3dfYnVnLmNnaT9pZD0xOTUwMTYpXFxuMy4gUmVtb3ZlIGdhcHMgYmV0d2VlbiB0YWJsZSBib3JkZXJzIGJ5IGRlZmF1bHQuXFxuKi9cXG5cXG50YWJsZSB7XFxuICB0ZXh0LWluZGVudDogMDsgLyogMSAqL1xcbiAgYm9yZGVyLWNvbG9yOiBpbmhlcml0OyAvKiAyICovXFxuICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOyAvKiAzICovXFxufVxcblxcbi8qXFxuMS4gQ2hhbmdlIHRoZSBmb250IHN0eWxlcyBpbiBhbGwgYnJvd3NlcnMuXFxuMi4gUmVtb3ZlIHRoZSBtYXJnaW4gaW4gRmlyZWZveCBhbmQgU2FmYXJpLlxcbjMuIFJlbW92ZSBkZWZhdWx0IHBhZGRpbmcgaW4gYWxsIGJyb3dzZXJzLlxcbiovXFxuXFxuYnV0dG9uLFxcbmlucHV0LFxcbm9wdGdyb3VwLFxcbnNlbGVjdCxcXG50ZXh0YXJlYSB7XFxuICBmb250LWZhbWlseTogaW5oZXJpdDsgLyogMSAqL1xcbiAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBpbmhlcml0OyAvKiAxICovXFxuICBmb250LXZhcmlhdGlvbi1zZXR0aW5nczogaW5oZXJpdDsgLyogMSAqL1xcbiAgZm9udC1zaXplOiAxMDAlOyAvKiAxICovXFxuICBmb250LXdlaWdodDogaW5oZXJpdDsgLyogMSAqL1xcbiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGxldHRlci1zcGFjaW5nOiBpbmhlcml0OyAvKiAxICovXFxuICBjb2xvcjogaW5oZXJpdDsgLyogMSAqL1xcbiAgbWFyZ2luOiAwOyAvKiAyICovXFxuICBwYWRkaW5nOiAwOyAvKiAzICovXFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBpbmhlcml0YW5jZSBvZiB0ZXh0IHRyYW5zZm9ybSBpbiBFZGdlIGFuZCBGaXJlZm94LlxcbiovXFxuXFxuYnV0dG9uLFxcbnNlbGVjdCB7XFxuICB0ZXh0LXRyYW5zZm9ybTogbm9uZTtcXG59XFxuXFxuLypcXG4xLiBDb3JyZWN0IHRoZSBpbmFiaWxpdHkgdG8gc3R5bGUgY2xpY2thYmxlIHR5cGVzIGluIGlPUyBhbmQgU2FmYXJpLlxcbjIuIFJlbW92ZSBkZWZhdWx0IGJ1dHRvbiBzdHlsZXMuXFxuKi9cXG5cXG5idXR0b24sXFxuaW5wdXQ6d2hlcmUoW3R5cGU9J2J1dHRvbiddKSxcXG5pbnB1dDp3aGVyZShbdHlwZT0ncmVzZXQnXSksXFxuaW5wdXQ6d2hlcmUoW3R5cGU9J3N1Ym1pdCddKSB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IGJ1dHRvbjsgLyogMSAqL1xcbiAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7IC8qIDIgKi9cXG4gIGJhY2tncm91bmQtaW1hZ2U6IG5vbmU7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5Vc2UgdGhlIG1vZGVybiBGaXJlZm94IGZvY3VzIHN0eWxlIGZvciBhbGwgZm9jdXNhYmxlIGVsZW1lbnRzLlxcbiovXFxuXFxuOi1tb3otZm9jdXNyaW5nIHtcXG4gIG91dGxpbmU6IGF1dG87XFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBhZGRpdGlvbmFsIGA6aW52YWxpZGAgc3R5bGVzIGluIEZpcmVmb3guIChodHRwczovL2dpdGh1Yi5jb20vbW96aWxsYS9nZWNrby1kZXYvYmxvYi8yZjllYWNkOWQzZDk5NWM5MzdiNDI1MWE1NTU3ZDk1ZDQ5NGM5YmUxL2xheW91dC9zdHlsZS9yZXMvZm9ybXMuY3NzI0w3MjgtTDczNylcXG4qL1xcblxcbjotbW96LXVpLWludmFsaWQge1xcbiAgYm94LXNoYWRvdzogbm9uZTtcXG59XFxuXFxuLypcXG5BZGQgdGhlIGNvcnJlY3QgdmVydGljYWwgYWxpZ25tZW50IGluIENocm9tZSBhbmQgRmlyZWZveC5cXG4qL1xcblxcbnByb2dyZXNzIHtcXG4gIHZlcnRpY2FsLWFsaWduOiBiYXNlbGluZTtcXG59XFxuXFxuLypcXG5Db3JyZWN0IHRoZSBjdXJzb3Igc3R5bGUgb2YgaW5jcmVtZW50IGFuZCBkZWNyZW1lbnQgYnV0dG9ucyBpbiBTYWZhcmkuXFxuKi9cXG5cXG46Oi13ZWJraXQtaW5uZXItc3Bpbi1idXR0b24sXFxuOjotd2Via2l0LW91dGVyLXNwaW4tYnV0dG9uIHtcXG4gIGhlaWdodDogYXV0bztcXG59XFxuXFxuLypcXG4xLiBDb3JyZWN0IHRoZSBvZGQgYXBwZWFyYW5jZSBpbiBDaHJvbWUgYW5kIFNhZmFyaS5cXG4yLiBDb3JyZWN0IHRoZSBvdXRsaW5lIHN0eWxlIGluIFNhZmFyaS5cXG4qL1xcblxcblt0eXBlPSdzZWFyY2gnXSB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IHRleHRmaWVsZDsgLyogMSAqL1xcbiAgb3V0bGluZS1vZmZzZXQ6IC0ycHg7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5SZW1vdmUgdGhlIGlubmVyIHBhZGRpbmcgaW4gQ2hyb21lIGFuZCBTYWZhcmkgb24gbWFjT1MuXFxuKi9cXG5cXG46Oi13ZWJraXQtc2VhcmNoLWRlY29yYXRpb24ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xcbn1cXG5cXG4vKlxcbjEuIENvcnJlY3QgdGhlIGluYWJpbGl0eSB0byBzdHlsZSBjbGlja2FibGUgdHlwZXMgaW4gaU9TIGFuZCBTYWZhcmkuXFxuMi4gQ2hhbmdlIGZvbnQgcHJvcGVydGllcyB0byBgaW5oZXJpdGAgaW4gU2FmYXJpLlxcbiovXFxuXFxuOjotd2Via2l0LWZpbGUtdXBsb2FkLWJ1dHRvbiB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IGJ1dHRvbjsgLyogMSAqL1xcbiAgZm9udDogaW5oZXJpdDsgLyogMiAqL1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBkaXNwbGF5IGluIENocm9tZSBhbmQgU2FmYXJpLlxcbiovXFxuXFxuc3VtbWFyeSB7XFxuICBkaXNwbGF5OiBsaXN0LWl0ZW07XFxufVxcblxcbi8qXFxuUmVtb3ZlcyB0aGUgZGVmYXVsdCBzcGFjaW5nIGFuZCBib3JkZXIgZm9yIGFwcHJvcHJpYXRlIGVsZW1lbnRzLlxcbiovXFxuXFxuYmxvY2txdW90ZSxcXG5kbCxcXG5kZCxcXG5oMSxcXG5oMixcXG5oMyxcXG5oNCxcXG5oNSxcXG5oNixcXG5ocixcXG5maWd1cmUsXFxucCxcXG5wcmUge1xcbiAgbWFyZ2luOiAwO1xcbn1cXG5cXG5maWVsZHNldCB7XFxuICBtYXJnaW46IDA7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG5cXG5sZWdlbmQge1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxub2wsXFxudWwsXFxubWVudSB7XFxuICBsaXN0LXN0eWxlOiBub25lO1xcbiAgbWFyZ2luOiAwO1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxuLypcXG5SZXNldCBkZWZhdWx0IHN0eWxpbmcgZm9yIGRpYWxvZ3MuXFxuKi9cXG5kaWFsb2cge1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxuLypcXG5QcmV2ZW50IHJlc2l6aW5nIHRleHRhcmVhcyBob3Jpem9udGFsbHkgYnkgZGVmYXVsdC5cXG4qL1xcblxcbnRleHRhcmVhIHtcXG4gIHJlc2l6ZTogdmVydGljYWw7XFxufVxcblxcbi8qXFxuMS4gUmVzZXQgdGhlIGRlZmF1bHQgcGxhY2Vob2xkZXIgb3BhY2l0eSBpbiBGaXJlZm94LiAoaHR0cHM6Ly9naXRodWIuY29tL3RhaWx3aW5kbGFicy90YWlsd2luZGNzcy9pc3N1ZXMvMzMwMClcXG4yLiBTZXQgdGhlIGRlZmF1bHQgcGxhY2Vob2xkZXIgY29sb3IgdG8gdGhlIHVzZXIncyBjb25maWd1cmVkIGdyYXkgNDAwIGNvbG9yLlxcbiovXFxuXFxuaW5wdXQ6Oi1tb3otcGxhY2Vob2xkZXIsIHRleHRhcmVhOjotbW96LXBsYWNlaG9sZGVyIHtcXG4gIG9wYWNpdHk6IDE7IC8qIDEgKi9cXG4gIGNvbG9yOiAjOWNhM2FmOyAvKiAyICovXFxufVxcblxcbmlucHV0OjpwbGFjZWhvbGRlcixcXG50ZXh0YXJlYTo6cGxhY2Vob2xkZXIge1xcbiAgb3BhY2l0eTogMTsgLyogMSAqL1xcbiAgY29sb3I6ICM5Y2EzYWY7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5TZXQgdGhlIGRlZmF1bHQgY3Vyc29yIGZvciBidXR0b25zLlxcbiovXFxuXFxuYnV0dG9uLFxcbltyb2xlPVxcXCJidXR0b25cXFwiXSB7XFxuICBjdXJzb3I6IHBvaW50ZXI7XFxufVxcblxcbi8qXFxuTWFrZSBzdXJlIGRpc2FibGVkIGJ1dHRvbnMgZG9uJ3QgZ2V0IHRoZSBwb2ludGVyIGN1cnNvci5cXG4qL1xcbjpkaXNhYmxlZCB7XFxuICBjdXJzb3I6IGRlZmF1bHQ7XFxufVxcblxcbi8qXFxuMS4gTWFrZSByZXBsYWNlZCBlbGVtZW50cyBgZGlzcGxheTogYmxvY2tgIGJ5IGRlZmF1bHQuIChodHRwczovL2dpdGh1Yi5jb20vbW96ZGV2cy9jc3NyZW1lZHkvaXNzdWVzLzE0KVxcbjIuIEFkZCBgdmVydGljYWwtYWxpZ246IG1pZGRsZWAgdG8gYWxpZ24gcmVwbGFjZWQgZWxlbWVudHMgbW9yZSBzZW5zaWJseSBieSBkZWZhdWx0LiAoaHR0cHM6Ly9naXRodWIuY29tL2plbnNpbW1vbnMvY3NzcmVtZWR5L2lzc3Vlcy8xNCNpc3N1ZWNvbW1lbnQtNjM0OTM0MjEwKVxcbiAgIFRoaXMgY2FuIHRyaWdnZXIgYSBwb29ybHkgY29uc2lkZXJlZCBsaW50IGVycm9yIGluIHNvbWUgdG9vbHMgYnV0IGlzIGluY2x1ZGVkIGJ5IGRlc2lnbi5cXG4qL1xcblxcbmltZyxcXG5zdmcsXFxudmlkZW8sXFxuY2FudmFzLFxcbmF1ZGlvLFxcbmlmcmFtZSxcXG5lbWJlZCxcXG5vYmplY3Qge1xcbiAgZGlzcGxheTogYmxvY2s7IC8qIDEgKi9cXG4gIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5Db25zdHJhaW4gaW1hZ2VzIGFuZCB2aWRlb3MgdG8gdGhlIHBhcmVudCB3aWR0aCBhbmQgcHJlc2VydmUgdGhlaXIgaW50cmluc2ljIGFzcGVjdCByYXRpby4gKGh0dHBzOi8vZ2l0aHViLmNvbS9tb3pkZXZzL2Nzc3JlbWVkeS9pc3N1ZXMvMTQpXFxuKi9cXG5cXG5pbWcsXFxudmlkZW8ge1xcbiAgbWF4LXdpZHRoOiAxMDAlO1xcbiAgaGVpZ2h0OiBhdXRvO1xcbn1cXG5cXG4vKiBNYWtlIGVsZW1lbnRzIHdpdGggdGhlIEhUTUwgaGlkZGVuIGF0dHJpYnV0ZSBzdGF5IGhpZGRlbiBieSBkZWZhdWx0ICovXFxuW2hpZGRlbl06d2hlcmUoOm5vdChbaGlkZGVuPVxcXCJ1bnRpbC1mb3VuZFxcXCJdKSkge1xcbiAgZGlzcGxheTogbm9uZTtcXG59XFxuXFxuW3R5cGU9J3RleHQnXSxpbnB1dDp3aGVyZSg6bm90KFt0eXBlXSkpLFt0eXBlPSdlbWFpbCddLFt0eXBlPSd1cmwnXSxbdHlwZT0ncGFzc3dvcmQnXSxbdHlwZT0nbnVtYmVyJ10sW3R5cGU9J2RhdGUnXSxbdHlwZT0nZGF0ZXRpbWUtbG9jYWwnXSxbdHlwZT0nbW9udGgnXSxbdHlwZT0nc2VhcmNoJ10sW3R5cGU9J3RlbCddLFt0eXBlPSd0aW1lJ10sW3R5cGU9J3dlZWsnXSxbbXVsdGlwbGVdLHRleHRhcmVhLHNlbGVjdCB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAtbW96LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAgICAgIGFwcGVhcmFuY2U6IG5vbmU7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xcbiAgYm9yZGVyLWNvbG9yOiAjNmI3MjgwO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItcmFkaXVzOiAwcHg7XFxuICBwYWRkaW5nLXRvcDogMC41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBmb250LXNpemU6IDFyZW07XFxuICBsaW5lLWhlaWdodDogMS41cmVtO1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG59XFxuXFxuW3R5cGU9J3RleHQnXTpmb2N1cywgaW5wdXQ6d2hlcmUoOm5vdChbdHlwZV0pKTpmb2N1cywgW3R5cGU9J2VtYWlsJ106Zm9jdXMsIFt0eXBlPSd1cmwnXTpmb2N1cywgW3R5cGU9J3Bhc3N3b3JkJ106Zm9jdXMsIFt0eXBlPSdudW1iZXInXTpmb2N1cywgW3R5cGU9J2RhdGUnXTpmb2N1cywgW3R5cGU9J2RhdGV0aW1lLWxvY2FsJ106Zm9jdXMsIFt0eXBlPSdtb250aCddOmZvY3VzLCBbdHlwZT0nc2VhcmNoJ106Zm9jdXMsIFt0eXBlPSd0ZWwnXTpmb2N1cywgW3R5cGU9J3RpbWUnXTpmb2N1cywgW3R5cGU9J3dlZWsnXTpmb2N1cywgW211bHRpcGxlXTpmb2N1cywgdGV4dGFyZWE6Zm9jdXMsIHNlbGVjdDpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLWluc2V0OiB2YXIoLS10dy1lbXB0eSwvKiEqLyAvKiEqLyk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAwcHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmZmO1xcbiAgLS10dy1yaW5nLWNvbG9yOiAjMjU2M2ViO1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDFweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdyk7XFxuICBib3JkZXItY29sb3I6ICMyNTYzZWI7XFxufVxcblxcbmlucHV0OjotbW96LXBsYWNlaG9sZGVyLCB0ZXh0YXJlYTo6LW1vei1wbGFjZWhvbGRlciB7XFxuICBjb2xvcjogIzZiNzI4MDtcXG4gIG9wYWNpdHk6IDE7XFxufVxcblxcbmlucHV0OjpwbGFjZWhvbGRlcix0ZXh0YXJlYTo6cGxhY2Vob2xkZXIge1xcbiAgY29sb3I6ICM2YjcyODA7XFxuICBvcGFjaXR5OiAxO1xcbn1cXG5cXG46Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1maWVsZHMtd3JhcHBlciB7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG5cXG46Oi13ZWJraXQtZGF0ZS1hbmQtdGltZS12YWx1ZSB7XFxuICBtaW4taGVpZ2h0OiAxLjVlbTtcXG4gIHRleHQtYWxpZ246IGluaGVyaXQ7XFxufVxcblxcbjo6LXdlYmtpdC1kYXRldGltZS1lZGl0IHtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbn1cXG5cXG46Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC15ZWFyLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LW1vbnRoLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LWRheS1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1ob3VyLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LW1pbnV0ZS1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1zZWNvbmQtZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtbWlsbGlzZWNvbmQtZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtbWVyaWRpZW0tZmllbGQge1xcbiAgcGFkZGluZy10b3A6IDA7XFxuICBwYWRkaW5nLWJvdHRvbTogMDtcXG59XFxuXFxuc2VsZWN0IHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHVybChcXFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9J25vbmUnIHZpZXdCb3g9JzAgMCAyMCAyMCclM2UlM2NwYXRoIHN0cm9rZT0nJTIzNmI3MjgwJyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIHN0cm9rZS13aWR0aD0nMS41JyBkPSdNNiA4bDQgNCA0LTQnLyUzZSUzYy9zdmclM2VcXFwiKTtcXG4gIGJhY2tncm91bmQtcG9zaXRpb246IHJpZ2h0IDAuNXJlbSBjZW50ZXI7XFxuICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xcbiAgYmFja2dyb3VuZC1zaXplOiAxLjVlbSAxLjVlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDIuNXJlbTtcXG4gIC13ZWJraXQtcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDtcXG4gICAgICAgICAgcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDtcXG59XFxuXFxuW211bHRpcGxlXSxbc2l6ZV06d2hlcmUoc2VsZWN0Om5vdChbc2l6ZT1cXFwiMVxcXCJdKSkge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogaW5pdGlhbDtcXG4gIGJhY2tncm91bmQtcG9zaXRpb246IGluaXRpYWw7XFxuICBiYWNrZ3JvdW5kLXJlcGVhdDogdW5zZXQ7XFxuICBiYWNrZ3JvdW5kLXNpemU6IGluaXRpYWw7XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgLXdlYmtpdC1wcmludC1jb2xvci1hZGp1c3Q6IHVuc2V0O1xcbiAgICAgICAgICBwcmludC1jb2xvci1hZGp1c3Q6IHVuc2V0O1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXSxbdHlwZT0ncmFkaW8nXSB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAtbW96LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAgICAgIGFwcGVhcmFuY2U6IG5vbmU7XFxuICBwYWRkaW5nOiAwO1xcbiAgLXdlYmtpdC1wcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0O1xcbiAgICAgICAgICBwcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0O1xcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xcbiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcXG4gIGJhY2tncm91bmQtb3JpZ2luOiBib3JkZXItYm94O1xcbiAgLXdlYmtpdC11c2VyLXNlbGVjdDogbm9uZTtcXG4gICAgIC1tb3otdXNlci1zZWxlY3Q6IG5vbmU7XFxuICAgICAgICAgIHVzZXItc2VsZWN0OiBub25lO1xcbiAgZmxleC1zaHJpbms6IDA7XFxuICBoZWlnaHQ6IDFyZW07XFxuICB3aWR0aDogMXJlbTtcXG4gIGNvbG9yOiAjMjU2M2ViO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcXG4gIGJvcmRlci1jb2xvcjogIzZiNzI4MDtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J10ge1xcbiAgYm9yZGVyLXJhZGl1czogMHB4O1xcbn1cXG5cXG5bdHlwZT0ncmFkaW8nXSB7XFxuICBib3JkZXItcmFkaXVzOiAxMDAlO1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTpmb2N1cyxbdHlwZT0ncmFkaW8nXTpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLWluc2V0OiB2YXIoLS10dy1lbXB0eSwvKiEqLyAvKiEqLyk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmZmO1xcbiAgLS10dy1yaW5nLWNvbG9yOiAjMjU2M2ViO1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmNoZWNrZWQsW3R5cGU9J3JhZGlvJ106Y2hlY2tlZCB7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgYmFja2dyb3VuZC1jb2xvcjogY3VycmVudENvbG9yO1xcbiAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7XFxuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XFxuICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTpjaGVja2VkIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHVybChcXFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB2aWV3Qm94PScwIDAgMTYgMTYnIGZpbGw9J3doaXRlJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNlJTNjcGF0aCBkPSdNMTIuMjA3IDQuNzkzYTEgMSAwIDAxMCAxLjQxNGwtNSA1YTEgMSAwIDAxLTEuNDE0IDBsLTItMmExIDEgMCAwMTEuNDE0LTEuNDE0TDYuNSA5LjA4Nmw0LjI5My00LjI5M2ExIDEgMCAwMTEuNDE0IDB6Jy8lM2UlM2Mvc3ZnJTNlXFxcIik7XFxufVxcblxcbkBtZWRpYSAoZm9yY2VkLWNvbG9yczogYWN0aXZlKSAge1xcblxcbiAgW3R5cGU9J2NoZWNrYm94J106Y2hlY2tlZCB7XFxuICAgIC13ZWJraXQtYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgLW1vei1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAgICAgIGFwcGVhcmFuY2U6IGF1dG87XFxuICB9XFxufVxcblxcblt0eXBlPSdyYWRpbyddOmNoZWNrZWQge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFxcXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHZpZXdCb3g9JzAgMCAxNiAxNicgZmlsbD0nd2hpdGUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM2UlM2NjaXJjbGUgY3g9JzgnIGN5PSc4JyByPSczJy8lM2UlM2Mvc3ZnJTNlXFxcIik7XFxufVxcblxcbkBtZWRpYSAoZm9yY2VkLWNvbG9yczogYWN0aXZlKSAge1xcblxcbiAgW3R5cGU9J3JhZGlvJ106Y2hlY2tlZCB7XFxuICAgIC13ZWJraXQtYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgLW1vei1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAgICAgIGFwcGVhcmFuY2U6IGF1dG87XFxuICB9XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmNoZWNrZWQ6aG92ZXIsW3R5cGU9J2NoZWNrYm94J106Y2hlY2tlZDpmb2N1cyxbdHlwZT0ncmFkaW8nXTpjaGVja2VkOmhvdmVyLFt0eXBlPSdyYWRpbyddOmNoZWNrZWQ6Zm9jdXMge1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIGJhY2tncm91bmQtY29sb3I6IGN1cnJlbnRDb2xvcjtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106aW5kZXRlcm1pbmF0ZSB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXFxcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMTYgMTYnJTNlJTNjcGF0aCBzdHJva2U9J3doaXRlJyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIHN0cm9rZS13aWR0aD0nMicgZD0nTTQgOGg4Jy8lM2UlM2Mvc3ZnJTNlXFxcIik7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgYmFja2dyb3VuZC1jb2xvcjogY3VycmVudENvbG9yO1xcbiAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7XFxuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XFxuICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xcbn1cXG5cXG5AbWVkaWEgKGZvcmNlZC1jb2xvcnM6IGFjdGl2ZSkgIHtcXG5cXG4gIFt0eXBlPSdjaGVja2JveCddOmluZGV0ZXJtaW5hdGUge1xcbiAgICAtd2Via2l0LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgIC1tb3otYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgICAgICBhcHBlYXJhbmNlOiBhdXRvO1xcbiAgfVxcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTppbmRldGVybWluYXRlOmhvdmVyLFt0eXBlPSdjaGVja2JveCddOmluZGV0ZXJtaW5hdGU6Zm9jdXMge1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIGJhY2tncm91bmQtY29sb3I6IGN1cnJlbnRDb2xvcjtcXG59XFxuXFxuW3R5cGU9J2ZpbGUnXSB7XFxuICBiYWNrZ3JvdW5kOiB1bnNldDtcXG4gIGJvcmRlci1jb2xvcjogaW5oZXJpdDtcXG4gIGJvcmRlci13aWR0aDogMDtcXG4gIGJvcmRlci1yYWRpdXM6IDA7XFxuICBwYWRkaW5nOiAwO1xcbiAgZm9udC1zaXplOiB1bnNldDtcXG4gIGxpbmUtaGVpZ2h0OiBpbmhlcml0O1xcbn1cXG5cXG5bdHlwZT0nZmlsZSddOmZvY3VzIHtcXG4gIG91dGxpbmU6IDFweCBzb2xpZCBCdXR0b25UZXh0O1xcbiAgb3V0bGluZTogMXB4IGF1dG8gLXdlYmtpdC1mb2N1cy1yaW5nLWNvbG9yO1xcbn1cXG4gIGh0bWwge1xcbiAgICBzY3JvbGwtYmVoYXZpb3I6IHNtb290aDtcXG4gIH1cXG4gIFxcbiAgYm9keSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTUgMjMgNDIgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjQzIDI0NCAyNDYgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGNvbG9yLCBiYWNrZ3JvdW5kLWNvbG9yLCBib3JkZXItY29sb3IsIHRleHQtZGVjb3JhdGlvbi1jb2xvciwgZmlsbCwgc3Ryb2tlO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAzMDBtcztcXG4gICAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS1iZyk7XFxuICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xcbn1cXG4gIFxcbiAgLyogRW5oYW5jZWQgQXJhYmljIGZvbnQgb3B0aW1pemF0aW9uICovXFxuICAuZm9udC1hcmFiaWMge1xcbiAgICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFxcXCJrZXJuXFxcIiAxLCBcXFwibGlnYVxcXCIgMSwgXFxcImNhbHRcXFwiIDEsIFxcXCJzczAxXFxcIiAxLCBcXFwic3MwMlxcXCIgMTtcXG4gICAgZm9udC12YXJpYW50LWxpZ2F0dXJlczogY29tbW9uLWxpZ2F0dXJlcyBjb250ZXh0dWFsO1xcbiAgICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5O1xcbiAgfVxcblxcbiAgLmZvbnQtY2Fpcm8ge1xcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1jYWlybyksICdDYWlybycsICdUYWphd2FsJywgJ05vdG8gU2FucyBBcmFiaWMnLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMSwgXFxcInNzMDFcXFwiIDE7XFxuICB9XFxuXFxuICAuZm9udC10YWphd2FsIHtcXG4gICAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtdGFqYXdhbCksICdUYWphd2FsJywgJ0NhaXJvJywgJ05vdG8gU2FucyBBcmFiaWMnLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMTtcXG4gIH1cXG4gIFxcbiAgLyogUlRMIHNwZWNpZmljIHN0eWxlcyAqL1xcbiAgW2Rpcj1cXFwicnRsXFxcIl0ge1xcbiAgICB0ZXh0LWFsaWduOiByaWdodDtcXG4gIH1cXG4gIFxcbiAgLyogQ3VzdG9tIHNjcm9sbGJhciAqL1xcbiAgOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICAgIHdpZHRoOiA4cHg7XFxuICB9XFxuICBcXG4gIDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MyAyNDQgMjQ2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4gIFxcbiAgOmlzKC5kYXJrICopOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzEgNDEgNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbiAgXFxuICA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMDkgMjEzIDIxOSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuICBcXG4gIDppcyguZGFyayAqKTo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDc1IDg1IDk5IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4gIFxcbiAgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTU2IDE2MyAxNzUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbiAgXFxuICA6aXMoLmRhcmsgKik6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxMDcgMTE0IDEyOCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmNvbnRhaW5lciB7XFxuICB3aWR0aDogMTAwJTtcXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuXFxuICAuY29udGFpbmVyIHtcXG4gICAgbWF4LXdpZHRoOiA2NDBweDtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XFxuXFxuICAuY29udGFpbmVyIHtcXG4gICAgbWF4LXdpZHRoOiA3NjhweDtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmNvbnRhaW5lciB7XFxuICAgIG1heC13aWR0aDogMTAyNHB4O1xcbiAgfVxcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogMTI4MHB4KSB7XFxuXFxuICAuY29udGFpbmVyIHtcXG4gICAgbWF4LXdpZHRoOiAxMjgwcHg7XFxuICB9XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiAxNTM2cHgpIHtcXG5cXG4gIC5jb250YWluZXIge1xcbiAgICBtYXgtd2lkdGg6IDE1MzZweDtcXG4gIH1cXG59XFxuLmdsYXNzIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlLWluLW91dDtcXG59XFxuLmdsYXNzOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XFxuICBib3gtc2hhZG93OiAwIDEycHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xcbn1cXG4uZ2xhc3MtYnV0dG9uIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcXG4gIHBhZGRpbmc6IDEycHggMjRweDtcXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgY3Vyc29yOiBwb2ludGVyO1xcbn1cXG4uZ2xhc3MtYnV0dG9uOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KSBzY2FsZSgxLjAyKTtcXG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcXG59XFxuLmdsYXNzLWNhcmQge1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyNHB4KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDI0cHgpO1xcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XFxuICBwYWRkaW5nOiAyNHB4O1xcbiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlLWluLW91dDtcXG59XFxuLmdsYXNzLWNhcmQ6aG92ZXIge1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEyKTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNHB4KTtcXG4gIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XFxufVxcbi8qIEVuaGFuY2VkIEJ1dHRvbiB2YXJpYW50cyB3aXRoIGdsYXNzIGVmZmVjdHMgKi9cXG4uYnRuLXByaW1hcnkge1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCB2YXIoLS10dy1ncmFkaWVudC1zdG9wcykpO1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiAjMDI4NGM3IHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDIgMTMyIDE5OSAvIDApIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtc3RvcHM6IHZhcigtLXR3LWdyYWRpZW50LWZyb20pLCB2YXIoLS10dy1ncmFkaWVudC10byk7XFxuICAtLXR3LWdyYWRpZW50LXRvOiAjMDM2OWExIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG4gIHBhZGRpbmctbGVmdDogMnJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDJyZW07XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDYwMDtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiKDAgMCAwIC8gMC4xKSwgMCA0cHggNnB4IC00cHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMTBweCAxNXB4IC0zcHggdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCA0cHggNnB4IC00cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zO1xcbn1cXG4uYnRuLXByaW1hcnk6aG92ZXIge1xcbiAgLS10dy10cmFuc2xhdGUteTogLTAuMjVyZW07XFxuICAtLXR3LXNjYWxlLXg6IDEuMDU7XFxuICAtLXR3LXNjYWxlLXk6IDEuMDU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogIzAzNjlhMSB2YXIoLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigzIDEwNSAxNjEgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbiAgLS10dy1ncmFkaWVudC10bzogIzA3NTk4NSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LXNoYWRvdzogMCAyMHB4IDI1cHggLTVweCByZ2IoMCAwIDAgLyAwLjEpLCAwIDhweCAxMHB4IC02cHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMjBweCAyNXB4IC01cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCA4cHggMTBweCAtNnB4IHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG4uYnRuLXByaW1hcnkge1xcbiAgICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICB9XFxuLmJ0bi1zZWNvbmRhcnkge1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuMik7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjEpO1xcbiAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgcGFkZGluZy1yaWdodDogMnJlbTtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICBmb250LXdlaWdodDogNjAwO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgNHB4IDZweCAtMXB4IHJnYigwIDAgMCAvIDAuMSksIDAgMnB4IDRweCAtMnB4IHJnYigwIDAgMCAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCA2cHggLTFweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDJweCA0cHggLTJweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMzAwbXM7XFxufVxcbi5idG4tc2Vjb25kYXJ5OmhvdmVyIHtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IC0wLjI1cmVtO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuMyk7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjIpO1xcbiAgLS10dy1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiKDAgMCAwIC8gMC4xKSwgMCA0cHggNnB4IC00cHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMTBweCAxNXB4IC0zcHggdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCA0cHggNnB4IC00cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcbi5idG4tc2Vjb25kYXJ5OmlzKC5kYXJrICopIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig1NiAxODkgMjQ4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5idG4tc2Vjb25kYXJ5IHtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gIH1cXG4uYnRuLW91dGxpbmUge1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyAwLjMpO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4wNSk7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNzVyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDU1IDY1IDgxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zO1xcbn1cXG4uYnRuLW91dGxpbmU6aG92ZXIge1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTU2IDE2MyAxNzUgLyAwLjUpO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4xKTtcXG59XFxuLmJ0bi1vdXRsaW5lOmlzKC5kYXJrICopIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDc1IDg1IDk5IC8gMC4zKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMDkgMjEzIDIxOSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uYnRuLW91dGxpbmU6aG92ZXI6aXMoLmRhcmsgKikge1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyAwLjUpO1xcbn1cXG4uYnRuLW91dGxpbmUge1xcbiAgICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xcbiAgfVxcbi8qIEVuaGFuY2VkIEdsYXNzIGVmZmVjdCBidXR0b25zICovXFxuLmJ0bi1nbGFzcyB7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG4gIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjEpO1xcbiAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgcGFkZGluZy1yaWdodDogMnJlbTtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICBmb250LXdlaWdodDogNjAwO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zO1xcbn1cXG4uYnRuLWdsYXNzOmhvdmVyIHtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IC0wLjI1cmVtO1xcbiAgLS10dy1zY2FsZS14OiAxLjA1O1xcbiAgLS10dy1zY2FsZS15OiAxLjA1O1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjIpO1xcbn1cXG4uYnRuLWdsYXNzIHtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG4uYnRuLWdsYXNzOmhvdmVyIHtcXG4gICAgYm94LXNoYWRvdzogMCAxMnB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcXG4gIH1cXG4vKiBFbmhhbmNlZCBnbGFzcyBidXR0b24gd2l0aCBwcmVtaXVtIGVmZmVjdHMgKi9cXG4vKiBQcmVtaXVtIGdsYXNzIGJ1dHRvbiBmb3IgaGVybyBhbmQgbWFpbiBDVEFzICovXFxuLmdsYXNzLWJ1dHRvbiB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xMikgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNikgMTAwJSk7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDI1cHgpO1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjVweCk7XFxuICAgIGJvcmRlcjogMS41cHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xcbiAgICBwYWRkaW5nOiAxcmVtIDJyZW07XFxuICAgIGZvbnQtc2l6ZTogMXJlbTtcXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG4uZ2xhc3MtYnV0dG9uOjpiZWZvcmUge1xcbiAgICBjb250ZW50OiAnJztcXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xcbiAgICB0b3A6IDA7XFxuICAgIGxlZnQ6IC0xMDAlO1xcbiAgICB3aWR0aDogMTAwJTtcXG4gICAgaGVpZ2h0OiAxMDAlO1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50IDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpIDUwJSwgdHJhbnNwYXJlbnQgMTAwJSk7XFxuICAgIHRyYW5zaXRpb246IGxlZnQgMC41cyBlYXNlO1xcbiAgfVxcbi5nbGFzcy1idXR0b246aG92ZXI6OmJlZm9yZSB7XFxuICAgIGxlZnQ6IDEwMCU7XFxuICB9XFxuLmdsYXNzLWJ1dHRvbjpob3ZlciB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KSBzY2FsZSgxLjAyKTtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE4KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEwKSAxMDAlKTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XFxuICAgIGJveC1zaGFkb3c6IDAgMTVweCA0NXB4IHJnYmEoMCwgMCwgMCwgMC4xNSksIDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgaW5zZXQ7XFxuICB9XFxuLyogR2xhc3MgY2FyZCBmb3IgaGVybyBzdGF0cyBhbmQgZmVhdHVyZSBjYXJkcyAqL1xcbi5nbGFzcy1jYXJkIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA0KSAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gICAgYm9yZGVyLXJhZGl1czogMTZweDtcXG4gICAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB9XFxuLmdsYXNzLWNhcmQ6aG92ZXIge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTIpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDYpIDEwMCUpO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCkgc2NhbGUoMS4wMik7XFxuICAgIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KTtcXG4gIH1cXG4vKiBFbmhhbmNlZCBDYXJkIHN0eWxlcyB3aXRoIGdsYXNzIGVmZmVjdHMgKi9cXG4uY2FyZC1wcmVtaXVtIHtcXG4gIGJvcmRlci1yYWRpdXM6IDEuNXJlbTtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogNTAwbXM7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMzBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigzMHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3gtc2hhZG93OiAwIDI1cHggNTBweCByZ2JhKDAsIDAsIDAsIDAuMSksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbn1cXG4uY2FyZC1wcmVtaXVtOmhvdmVyIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpIHNjYWxlKDEuMDMpO1xcbiAgICBib3gtc2hhZG93OiAwIDM1cHggNzBweCByZ2JhKDAsIDAsIDAsIDAuMTUpLCBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIH1cXG4vKiBJbnB1dCBzdHlsZXMgKi9cXG4vKiBTZWN0aW9uIHNwYWNpbmcgKi9cXG4uc2VjdGlvbi1wYWRkaW5nIHtcXG4gIHBhZGRpbmctdG9wOiA0cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDRyZW07XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5zZWN0aW9uLXBhZGRpbmcge1xcbiAgICBwYWRkaW5nLXRvcDogNnJlbTtcXG4gICAgcGFkZGluZy1ib3R0b206IDZyZW07XFxuICB9XFxufVxcbi5jb250YWluZXItcGFkZGluZyB7XFxuICBwYWRkaW5nLWxlZnQ6IDFyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogNjQwcHgpIHtcXG5cXG4gIC5jb250YWluZXItcGFkZGluZyB7XFxuICAgIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICB9XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5jb250YWluZXItcGFkZGluZyB7XFxuICAgIHBhZGRpbmctbGVmdDogMnJlbTtcXG4gICAgcGFkZGluZy1yaWdodDogMnJlbTtcXG4gIH1cXG59XFxuLyogRW5oYW5jZWQgVHlwb2dyYXBoeSB3aXRoIHByZW1pdW0gc3R5bGluZyAtIERhcmsgdGhlbWUgb3B0aW1pemVkICovXFxuLmhlYWRpbmctaGVybyB7XFxuICAgIGZvbnQtc2l6ZTogY2xhbXAoMy41cmVtLCAxMnZ3LCA4cmVtKTtcXG4gICAgZm9udC13ZWlnaHQ6IDkwMDtcXG4gICAgbGluZS1oZWlnaHQ6IDAuODU7XFxuICAgIGxldHRlci1zcGFjaW5nOiAtMC4wNGVtO1xcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1jYWlybyksICdDYWlybycsICdUYWphd2FsJywgJ05vdG8gU2FucyBBcmFiaWMnLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMSwgXFxcInNzMDFcXFwiIDE7XFxuICAgIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWNhaXJvKSwgQ2Fpcm8sIFRhamF3YWwsIE5vdG8gU2FucyBBcmFiaWMsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZmZmZiAwJSwgI2YxZjVmOSA1MCUsICNlMmU4ZjAgMTAwJSk7XFxuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gICAgdGV4dC1zaGFkb3c6IDAgOHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjQpO1xcbiAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMikpO1xcbiAgfVxcbi5oZWFkaW5nLWxnIHtcXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWRpc3BsYXkpLCBQb3BwaW5zLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxuICBmb250LXNpemU6IDEuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDIuMjVyZW07XFxuICBmb250LXdlaWdodDogNzAwO1xcbiAgbGV0dGVyLXNwYWNpbmc6IC0wLjAyNWVtO1xcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogMTAyNHB4KSB7XFxuXFxuICAuaGVhZGluZy1sZyB7XFxuICAgIGZvbnQtc2l6ZTogMi4yNXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDIuNXJlbTtcXG4gIH1cXG59XFxuLmhlYWRpbmctbWQge1xcbiAgZm9udC1zaXplOiAxLjVyZW07XFxuICBsaW5lLWhlaWdodDogMnJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XFxuICBsZXR0ZXItc3BhY2luZzogLTAuMDI1ZW07XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5oZWFkaW5nLW1kIHtcXG4gICAgZm9udC1zaXplOiAxLjg3NXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDIuMjVyZW07XFxuICB9XFxufVxcbi5oZWFkaW5nLXNtIHtcXG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjc1cmVtO1xcbiAgZm9udC13ZWlnaHQ6IDYwMDtcXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmhlYWRpbmctc20ge1xcbiAgICBmb250LXNpemU6IDEuNXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDJyZW07XFxuICB9XFxufVxcbi8qIEVuaGFuY2VkIGdyYWRpZW50IHRleHQgZWZmZWN0cyAqL1xcbi5ncmFkaWVudC10ZXh0IHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdmFyKC0tdHctZ3JhZGllbnQtc3RvcHMpKTtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogIzAyODRjNyB2YXIoLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigyIDEzMiAxOTkgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDIxNyA3MCAyMzkgLyAwKSAgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksICNkOTQ2ZWYgdmFyKC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbiAgLS10dy1ncmFkaWVudC10bzogIzAzNjlhMSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgICAgICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDIwMCUgMjAwJTtcXG4gICAgYW5pbWF0aW9uOiBncmFkaWVudFNoaWZ0IDNzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbn1cXG4vKiBQcmVtaXVtIEdvbGQvTWV0YWxsaWMgVGV4dCBFZmZlY3RzICovXFxuLmdyYWRpZW50LXRleHQtZ29sZCB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmQ3MDAgMCUsICNmZmVkNGUgMjUlLCAjZmZkNzAwIDUwJSwgI2ZmYjMwMCA3NSUsICNmZmQ3MDAgMTAwJSk7XFxuICAgIGJhY2tncm91bmQtc2l6ZTogNDAwJSA0MDAlO1xcbiAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICAgIGFuaW1hdGlvbjogZ29sZFNoaW5lIDNzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgICB0ZXh0LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjQpO1xcbiAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDhweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpKTtcXG4gIH1cXG4vKiBTaGFycCB2ZXJzaW9uIHdpdGhvdXQgYmx1ciBlZmZlY3RzIGZvciBoZXJvIHRpdGxlICovXFxuLmdyYWRpZW50LXRleHQtZ29sZC1wcmVtaXVtLXNoYXJwIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLFxcbiAgICAgICM4QjY5MTQgMCUsXFxuICAgICAgI0I4ODYwQiAxMCUsXFxuICAgICAgI0RBQTUyMCAyMCUsXFxuICAgICAgI0ZGRDcwMCAzMCUsXFxuICAgICAgI0ZGRUQ0RSA0MCUsXFxuICAgICAgI0ZGRjhEQyA1MCUsXFxuICAgICAgI0ZGRUQ0RSA2MCUsXFxuICAgICAgI0ZGRDcwMCA3MCUsXFxuICAgICAgI0RBQTUyMCA4MCUsXFxuICAgICAgI0I4ODYwQiA5MCUsXFxuICAgICAgIzhCNjkxNCAxMDAlKTtcXG4gICAgYmFja2dyb3VuZC1zaXplOiAzMDAlIDEwMCU7XFxuICAgIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gICAgYW5pbWF0aW9uOiBwcmVtaXVtR29sZFNoaW5lU2hhcnAgNHMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XFxuICAgIC8qIFJlbW92ZWQgYWxsIGJsdXItcmVsYXRlZCBwcm9wZXJ0aWVzICovXFxuICAgIHRleHQtc2hhZG93OiAwIDRweCA4cHggcmdiYSgyNTUsIDIxNSwgMCwgMC40KTtcXG4gICAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDJweCA0cHggcmdiYSgyNTUsIDIxNSwgMCwgMC4zKSk7XFxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcXG4gICAgLyogRW5oYW5jZWQgdGV4dCByZW5kZXJpbmcgZm9yIHNoYXJwbmVzcyAqL1xcbiAgICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5O1xcbiAgICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcXG4gICAgLW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZTtcXG4gICAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBcXFwia2VyblxcXCIgMSwgXFxcImxpZ2FcXFwiIDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWigwKTtcXG4gICAgd2lsbC1jaGFuZ2U6IGJhY2tncm91bmQtcG9zaXRpb247XFxuICAgIGJhY2tmYWNlLXZpc2liaWxpdHk6IGhpZGRlbjtcXG4gIH1cXG4vKiBFbmhhbmNlZCBBcmFiaWMgdHlwb2dyYXBoeSAqL1xcbi50ZXh0LWFyYWJpYyB7XFxuICAgIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWNhaXJvKSwgJ0NhaXJvJywgJ1RhamF3YWwnLCAnTm90byBTYW5zIEFyYWJpYycsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjtcXG4gICAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBcXFwia2VyblxcXCIgMSwgXFxcImxpZ2FcXFwiIDEsIFxcXCJjYWx0XFxcIiAxLCBcXFwic3MwMVxcXCIgMTtcXG4gICAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtY2Fpcm8pLCBDYWlybywgVGFqYXdhbCwgTm90byBTYW5zIEFyYWJpYywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbiAgICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFxcXCJrZXJuXFxcIiAxLCBcXFwibGlnYVxcXCIgMSwgXFxcImNhbHRcXFwiIDEsIFxcXCJzczAxXFxcIiAxLCBcXFwic3MwMlxcXCIgMTtcXG4gICAgZm9udC12YXJpYW50LWxpZ2F0dXJlczogY29tbW9uLWxpZ2F0dXJlcyBjb250ZXh0dWFsO1xcbiAgICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5O1xcbiAgfVxcbi50ZXh0LWFyYWJpYy1wcmVtaXVtIHtcXG4gICAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtY2Fpcm8pLCAnQ2Fpcm8nLCAnVGFqYXdhbCcsICdOb3RvIFNhbnMgQXJhYmljJywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbiAgICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFxcXCJrZXJuXFxcIiAxLCBcXFwibGlnYVxcXCIgMSwgXFxcImNhbHRcXFwiIDEsIFxcXCJzczAxXFxcIiAxO1xcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1jYWlybyksIENhaXJvLCBUYWphd2FsLCBOb3RvIFNhbnMgQXJhYmljLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMSwgXFxcInNzMDFcXFwiIDEsIFxcXCJzczAyXFxcIiAxO1xcbiAgICBmb250LXZhcmlhbnQtbGlnYXR1cmVzOiBjb21tb24tbGlnYXR1cmVzIGNvbnRleHR1YWw7XFxuICAgIHRleHQtcmVuZGVyaW5nOiBvcHRpbWl6ZUxlZ2liaWxpdHk7XFxuICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xcbiAgICB0ZXh0LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7XFxuICAgIGxldHRlci1zcGFjaW5nOiAwLjAyNWVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4zO1xcbiAgICBmb250LXdlaWdodDogNzAwO1xcbiAgfVxcbi8qIFByZW1pdW0gc2VjdGlvbiBoZWFkZXJzIHdpdGggU3lyaWFuIGN1bHR1cmFsIGVsZW1lbnRzICovXFxuLyogR29sZCBhY2NlbnQgZWxlbWVudHMgKi9cXG4vKiBQcmVtaXVtIGdsYXNzIG1vcnBoaXNtIGNhcmRzICovXFxuLyogR29sZCBncmFkaWVudCBidXR0b25zICovXFxuLyogU2hpbW1lciBlZmZlY3QgZm9yIGJ1dHRvbnMgYW5kIGNhcmRzICovXFxuLyogUG9wdWxhciBiYWRnZSBzdHlsaW5nICovXFxuLyogVGFiIG5hdmlnYXRpb24gcHJlbWl1bSBzdHlsaW5nICovXFxuLyogUHJlbWl1bSBHb2xkL01ldGFsbGljIEJ1dHRvbnMgKi9cXG4uYnRuLWdvbGQtcHJlbWl1bSB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsXFxuICAgICAgIzhCNjkxNCAwJSxcXG4gICAgICAjQjg4NjBCIDE1JSxcXG4gICAgICAjREFBNTIwIDMwJSxcXG4gICAgICAjRkZENzAwIDQ1JSxcXG4gICAgICAjRkZFRDRFIDYwJSxcXG4gICAgICAjRkZENzAwIDc1JSxcXG4gICAgICAjREFBNTIwIDkwJSxcXG4gICAgICAjQjg4NjBCIDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDMwMCUgMzAwJTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDIxNSwgMCwgMC40KTtcXG4gICAgYm94LXNoYWRvdzpcXG4gICAgICAwIDhweCAyNXB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuNCksXFxuICAgICAgMCA0cHggMTVweCByZ2JhKDE4NCwgMTM0LCAxMSwgMC4zKSxcXG4gICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSxcXG4gICAgICBpbnNldCAwIC0xcHggMCByZ2JhKDEzOSwgMTA1LCAyMCwgMC4yKTtcXG4gICAgY29sb3I6ICMxYTFhMWE7XFxuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XFxuICAgIHRleHQtc2hhZG93OiAwIDFweCAycHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpO1xcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xcbiAgICBwYWRkaW5nOiAxcmVtIDJyZW07XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICAgIG92ZXJmbG93OiBoaWRkZW47XFxuICAgIGFuaW1hdGlvbjogZ29sZFNoaW5lIDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgfVxcbi5idG4tZ29sZC1wcmVtaXVtOjpiZWZvcmUge1xcbiAgICBjb250ZW50OiAnJztcXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xcbiAgICB0b3A6IDA7XFxuICAgIGxlZnQ6IC0xMDAlO1xcbiAgICB3aWR0aDogMTAwJTtcXG4gICAgaGVpZ2h0OiAxMDAlO1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50IDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNCkgNTAlLCB0cmFuc3BhcmVudCAxMDAlKTtcXG4gICAgdHJhbnNpdGlvbjogbGVmdCAwLjZzIGVhc2U7XFxuICB9XFxuLmJ0bi1nb2xkLXByZW1pdW06aG92ZXI6OmJlZm9yZSB7XFxuICAgIGxlZnQ6IDEwMCU7XFxuICB9XFxuLmJ0bi1nb2xkLXByZW1pdW06aG92ZXIge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTNweCkgc2NhbGUoMS4wNSk7XFxuICAgIGJveC1zaGFkb3c6XFxuICAgICAgMCAxNXB4IDQwcHggcmdiYSgyNTUsIDIxNSwgMCwgMC41KSxcXG4gICAgICAwIDhweCAyNXB4IHJnYmEoMTg0LCAxMzQsIDExLCAwLjQpLFxcbiAgICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpLFxcbiAgICAgIGluc2V0IDAgLTFweCAwIHJnYmEoMTM5LCAxMDUsIDIwLCAwLjMpO1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAxMDAlIDEwMCU7XFxuICAgIGZpbHRlcjogYnJpZ2h0bmVzcygxLjEpIHNhdHVyYXRlKDEuMSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDIxNSwgMCwgMC42KTtcXG4gIH1cXG4vKiBNZXRhbGxpYyBDYXJkIEVmZmVjdHMgKi9cXG4uY2FyZC1tZXRhbGxpYy1nb2xkIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDIxNSwgMCwgMC4xKSAwJSwgcmdiYSgyNTUsIDIxNSwgMCwgMC4wNSkgMTAwJSk7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDI1cHgpO1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjVweCk7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyMTUsIDAsIDAuMik7XFxuICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7XFxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgyNTUsIDIxNSwgMCwgMC4xKSwgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICAgIG92ZXJmbG93OiBoaWRkZW47XFxuICB9XFxuLmNhcmQtbWV0YWxsaWMtZ29sZDo6YmVmb3JlIHtcXG4gICAgY29udGVudDogJyc7XFxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcXG4gICAgdG9wOiAtNTAlO1xcbiAgICBsZWZ0OiAtNTAlO1xcbiAgICB3aWR0aDogMjAwJTtcXG4gICAgaGVpZ2h0OiAyMDAlO1xcbiAgICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDI1NSwgMjE1LCAwLCAwLjEpIDAlLCB0cmFuc3BhcmVudCA3MCUpO1xcbiAgICBhbmltYXRpb246IGdvbGRHbG93IDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcXG4gIH1cXG4uY2FyZC1tZXRhbGxpYy1nb2xkOmhvdmVyIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpIHNjYWxlKDEuMDIpO1xcbiAgICBib3gtc2hhZG93OiAwIDIwcHggNjBweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjIpLCBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjQpO1xcbiAgfVxcbi8qIEFuaW1hdGlvbiB1dGlsaXRpZXMgKi9cXG4ucG9pbnRlci1ldmVudHMtbm9uZSB7XFxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcXG59XFxuLnZpc2libGUge1xcbiAgdmlzaWJpbGl0eTogdmlzaWJsZTtcXG59XFxuLnN0YXRpYyB7XFxuICBwb3NpdGlvbjogc3RhdGljO1xcbn1cXG4uZml4ZWQge1xcbiAgcG9zaXRpb246IGZpeGVkO1xcbn1cXG4uYWJzb2x1dGUge1xcbiAgcG9zaXRpb246IGFic29sdXRlO1xcbn1cXG4ucmVsYXRpdmUge1xcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcbn1cXG4uLWluc2V0LTQge1xcbiAgaW5zZXQ6IC0xcmVtO1xcbn1cXG4uaW5zZXQtMCB7XFxuICBpbnNldDogMHB4O1xcbn1cXG4uLXJpZ2h0LTEge1xcbiAgcmlnaHQ6IC0wLjI1cmVtO1xcbn1cXG4uLXJpZ2h0LTIge1xcbiAgcmlnaHQ6IC0wLjVyZW07XFxufVxcbi4tdG9wLTEge1xcbiAgdG9wOiAtMC4yNXJlbTtcXG59XFxuLi10b3AtMiB7XFxuICB0b3A6IC0wLjVyZW07XFxufVxcbi4tdG9wLTYge1xcbiAgdG9wOiAtMS41cmVtO1xcbn1cXG4uYm90dG9tLTAge1xcbiAgYm90dG9tOiAwcHg7XFxufVxcbi5ib3R0b20tMjgge1xcbiAgYm90dG9tOiA3cmVtO1xcbn1cXG4uYm90dG9tLTMyIHtcXG4gIGJvdHRvbTogOHJlbTtcXG59XFxuLmJvdHRvbS0zNiB7XFxuICBib3R0b206IDlyZW07XFxufVxcbi5ib3R0b20tNDAge1xcbiAgYm90dG9tOiAxMHJlbTtcXG59XFxuLmJvdHRvbS02IHtcXG4gIGJvdHRvbTogMS41cmVtO1xcbn1cXG4uYm90dG9tLTgge1xcbiAgYm90dG9tOiAycmVtO1xcbn1cXG4ubGVmdC0wIHtcXG4gIGxlZnQ6IDBweDtcXG59XFxuLmxlZnQtMVxcXFwvMiB7XFxuICBsZWZ0OiA1MCU7XFxufVxcbi5sZWZ0LTEwIHtcXG4gIGxlZnQ6IDIuNXJlbTtcXG59XFxuLmxlZnQtMTIge1xcbiAgbGVmdDogM3JlbTtcXG59XFxuLmxlZnQtMTQge1xcbiAgbGVmdDogMy41cmVtO1xcbn1cXG4ubGVmdC0xNiB7XFxuICBsZWZ0OiA0cmVtO1xcbn1cXG4ubGVmdC0yMCB7XFxuICBsZWZ0OiA1cmVtO1xcbn1cXG4ubGVmdC02IHtcXG4gIGxlZnQ6IDEuNXJlbTtcXG59XFxuLmxlZnQtOCB7XFxuICBsZWZ0OiAycmVtO1xcbn1cXG4ucmlnaHQtMCB7XFxuICByaWdodDogMHB4O1xcbn1cXG4ucmlnaHQtMSB7XFxuICByaWdodDogMC4yNXJlbTtcXG59XFxuLnJpZ2h0LTFcXFxcLzQge1xcbiAgcmlnaHQ6IDI1JTtcXG59XFxuLnJpZ2h0LTEwIHtcXG4gIHJpZ2h0OiAyLjVyZW07XFxufVxcbi5yaWdodC0xMiB7XFxuICByaWdodDogM3JlbTtcXG59XFxuLnJpZ2h0LTE2IHtcXG4gIHJpZ2h0OiA0cmVtO1xcbn1cXG4ucmlnaHQtMjAge1xcbiAgcmlnaHQ6IDVyZW07XFxufVxcbi5yaWdodC00IHtcXG4gIHJpZ2h0OiAxcmVtO1xcbn1cXG4ucmlnaHQtNiB7XFxuICByaWdodDogMS41cmVtO1xcbn1cXG4ucmlnaHQtZnVsbCB7XFxuICByaWdodDogMTAwJTtcXG59XFxuLnRvcC0wIHtcXG4gIHRvcDogMHB4O1xcbn1cXG4udG9wLTEge1xcbiAgdG9wOiAwLjI1cmVtO1xcbn1cXG4udG9wLTFcXFxcLzIge1xcbiAgdG9wOiA1MCU7XFxufVxcbi50b3AtMjAge1xcbiAgdG9wOiA1cmVtO1xcbn1cXG4udG9wLTI0IHtcXG4gIHRvcDogNnJlbTtcXG59XFxuLnRvcC0yOCB7XFxuICB0b3A6IDdyZW07XFxufVxcbi50b3AtMzIge1xcbiAgdG9wOiA4cmVtO1xcbn1cXG4udG9wLTQge1xcbiAgdG9wOiAxcmVtO1xcbn1cXG4udG9wLTQwIHtcXG4gIHRvcDogMTByZW07XFxufVxcbi50b3AtNiB7XFxuICB0b3A6IDEuNXJlbTtcXG59XFxuLmlzb2xhdGUge1xcbiAgaXNvbGF0aW9uOiBpc29sYXRlO1xcbn1cXG4uLXotMTAge1xcbiAgei1pbmRleDogLTEwO1xcbn1cXG4uei0xMCB7XFxuICB6LWluZGV4OiAxMDtcXG59XFxuLnotMjAge1xcbiAgei1pbmRleDogMjA7XFxufVxcbi56LTUwIHtcXG4gIHotaW5kZXg6IDUwO1xcbn1cXG4uei1cXFxcWzk5OTlcXFxcXSB7XFxuICB6LWluZGV4OiA5OTk5O1xcbn1cXG4uLW0tNCB7XFxuICBtYXJnaW46IC0xcmVtO1xcbn1cXG4uLW0tNiB7XFxuICBtYXJnaW46IC0xLjVyZW07XFxufVxcbi5teC1hdXRvIHtcXG4gIG1hcmdpbi1sZWZ0OiBhdXRvO1xcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xcbn1cXG4ubWItMSB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xcbn1cXG4ubWItMTAge1xcbiAgbWFyZ2luLWJvdHRvbTogMi41cmVtO1xcbn1cXG4ubWItMTIge1xcbiAgbWFyZ2luLWJvdHRvbTogM3JlbTtcXG59XFxuLm1iLTE2IHtcXG4gIG1hcmdpbi1ib3R0b206IDRyZW07XFxufVxcbi5tYi0yIHtcXG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcXG59XFxuLm1iLTIwIHtcXG4gIG1hcmdpbi1ib3R0b206IDVyZW07XFxufVxcbi5tYi0zIHtcXG4gIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XFxufVxcbi5tYi00IHtcXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XFxufVxcbi5tYi02IHtcXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcXG59XFxuLm1iLTgge1xcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcXG59XFxuLm1lLTQge1xcbiAgbWFyZ2luLWlubGluZS1lbmQ6IDFyZW07XFxufVxcbi5tci0zIHtcXG4gIG1hcmdpbi1yaWdodDogMC43NXJlbTtcXG59XFxuLm10LTBcXFxcLjUge1xcbiAgbWFyZ2luLXRvcDogMC4xMjVyZW07XFxufVxcbi5tdC0xNiB7XFxuICBtYXJnaW4tdG9wOiA0cmVtO1xcbn1cXG4ubXQtMiB7XFxuICBtYXJnaW4tdG9wOiAwLjVyZW07XFxufVxcbi5tdC0yMCB7XFxuICBtYXJnaW4tdG9wOiA1cmVtO1xcbn1cXG4ubXQtNCB7XFxuICBtYXJnaW4tdG9wOiAxcmVtO1xcbn1cXG4ubXQtNiB7XFxuICBtYXJnaW4tdG9wOiAxLjVyZW07XFxufVxcbi5tdC04IHtcXG4gIG1hcmdpbi10b3A6IDJyZW07XFxufVxcbi5ibG9jayB7XFxuICBkaXNwbGF5OiBibG9jaztcXG59XFxuLmlubGluZS1ibG9jayB7XFxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XFxufVxcbi5mbGV4IHtcXG4gIGRpc3BsYXk6IGZsZXg7XFxufVxcbi5pbmxpbmUtZmxleCB7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG59XFxuLmdyaWQge1xcbiAgZGlzcGxheTogZ3JpZDtcXG59XFxuLmhpZGRlbiB7XFxuICBkaXNwbGF5OiBub25lO1xcbn1cXG4uaC0xIHtcXG4gIGhlaWdodDogMC4yNXJlbTtcXG59XFxuLmgtMVxcXFwuNSB7XFxuICBoZWlnaHQ6IDAuMzc1cmVtO1xcbn1cXG4uaC0xMCB7XFxuICBoZWlnaHQ6IDIuNXJlbTtcXG59XFxuLmgtMTIge1xcbiAgaGVpZ2h0OiAzcmVtO1xcbn1cXG4uaC0xNCB7XFxuICBoZWlnaHQ6IDMuNXJlbTtcXG59XFxuLmgtMTYge1xcbiAgaGVpZ2h0OiA0cmVtO1xcbn1cXG4uaC0yIHtcXG4gIGhlaWdodDogMC41cmVtO1xcbn1cXG4uaC0yMCB7XFxuICBoZWlnaHQ6IDVyZW07XFxufVxcbi5oLTI0IHtcXG4gIGhlaWdodDogNnJlbTtcXG59XFxuLmgtMjgge1xcbiAgaGVpZ2h0OiA3cmVtO1xcbn1cXG4uaC0zIHtcXG4gIGhlaWdodDogMC43NXJlbTtcXG59XFxuLmgtMzIge1xcbiAgaGVpZ2h0OiA4cmVtO1xcbn1cXG4uaC0zNiB7XFxuICBoZWlnaHQ6IDlyZW07XFxufVxcbi5oLTQge1xcbiAgaGVpZ2h0OiAxcmVtO1xcbn1cXG4uaC00MCB7XFxuICBoZWlnaHQ6IDEwcmVtO1xcbn1cXG4uaC01IHtcXG4gIGhlaWdodDogMS4yNXJlbTtcXG59XFxuLmgtNiB7XFxuICBoZWlnaHQ6IDEuNXJlbTtcXG59XFxuLmgtNjQge1xcbiAgaGVpZ2h0OiAxNnJlbTtcXG59XFxuLmgtNyB7XFxuICBoZWlnaHQ6IDEuNzVyZW07XFxufVxcbi5oLTgge1xcbiAgaGVpZ2h0OiAycmVtO1xcbn1cXG4uaC1mdWxsIHtcXG4gIGhlaWdodDogMTAwJTtcXG59XFxuLmgtMFxcXFwuNSB7XFxuICBoZWlnaHQ6IDAuMTI1cmVtO1xcbn1cXG4ubWluLWgtc2NyZWVuIHtcXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xcbn1cXG4udy0xIHtcXG4gIHdpZHRoOiAwLjI1cmVtO1xcbn1cXG4udy0xXFxcXC41IHtcXG4gIHdpZHRoOiAwLjM3NXJlbTtcXG59XFxuLnctMTAge1xcbiAgd2lkdGg6IDIuNXJlbTtcXG59XFxuLnctMTIge1xcbiAgd2lkdGg6IDNyZW07XFxufVxcbi53LTE0IHtcXG4gIHdpZHRoOiAzLjVyZW07XFxufVxcbi53LTE2IHtcXG4gIHdpZHRoOiA0cmVtO1xcbn1cXG4udy0yIHtcXG4gIHdpZHRoOiAwLjVyZW07XFxufVxcbi53LTIwIHtcXG4gIHdpZHRoOiA1cmVtO1xcbn1cXG4udy0yNCB7XFxuICB3aWR0aDogNnJlbTtcXG59XFxuLnctMjgge1xcbiAgd2lkdGg6IDdyZW07XFxufVxcbi53LTMge1xcbiAgd2lkdGg6IDAuNzVyZW07XFxufVxcbi53LTMyIHtcXG4gIHdpZHRoOiA4cmVtO1xcbn1cXG4udy0zNiB7XFxuICB3aWR0aDogOXJlbTtcXG59XFxuLnctNCB7XFxuICB3aWR0aDogMXJlbTtcXG59XFxuLnctNDAge1xcbiAgd2lkdGg6IDEwcmVtO1xcbn1cXG4udy01IHtcXG4gIHdpZHRoOiAxLjI1cmVtO1xcbn1cXG4udy02IHtcXG4gIHdpZHRoOiAxLjVyZW07XFxufVxcbi53LTcge1xcbiAgd2lkdGg6IDEuNzVyZW07XFxufVxcbi53LTgge1xcbiAgd2lkdGg6IDJyZW07XFxufVxcbi53LWZ1bGwge1xcbiAgd2lkdGg6IDEwMCU7XFxufVxcbi5tYXgtdy0yeGwge1xcbiAgbWF4LXdpZHRoOiA0MnJlbTtcXG59XFxuLm1heC13LTN4bCB7XFxuICBtYXgtd2lkdGg6IDQ4cmVtO1xcbn1cXG4ubWF4LXctNHhsIHtcXG4gIG1heC13aWR0aDogNTZyZW07XFxufVxcbi5tYXgtdy01eGwge1xcbiAgbWF4LXdpZHRoOiA2NHJlbTtcXG59XFxuLm1heC13LTZ4bCB7XFxuICBtYXgtd2lkdGg6IDcycmVtO1xcbn1cXG4ubWF4LXctN3hsIHtcXG4gIG1heC13aWR0aDogODByZW07XFxufVxcbi5tYXgtdy1sZyB7XFxuICBtYXgtd2lkdGg6IDMycmVtO1xcbn1cXG4ubWF4LXctbWQge1xcbiAgbWF4LXdpZHRoOiAyOHJlbTtcXG59XFxuLmZsZXgtMSB7XFxuICBmbGV4OiAxIDEgMCU7XFxufVxcbi5mbGV4LXNocmluay0wIHtcXG4gIGZsZXgtc2hyaW5rOiAwO1xcbn1cXG4uLXRyYW5zbGF0ZS14LTFcXFxcLzIge1xcbiAgLS10dy10cmFuc2xhdGUteDogLTUwJTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4uLXRyYW5zbGF0ZS14LWZ1bGwge1xcbiAgLS10dy10cmFuc2xhdGUteDogLTEwMCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLi10cmFuc2xhdGUteS0xXFxcXC8yIHtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IC01MCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLi1za2V3LXgtMTIge1xcbiAgLS10dy1za2V3LXg6IC0xMmRlZztcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4uc2NhbGUtMTA1IHtcXG4gIC0tdHctc2NhbGUteDogMS4wNTtcXG4gIC0tdHctc2NhbGUteTogMS4wNTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4uc2NhbGUteC0wIHtcXG4gIC0tdHctc2NhbGUteDogMDtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4udHJhbnNmb3JtIHtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcXG5cXG4gIDUwJSB7XFxuICAgIG9wYWNpdHk6IC41O1xcbiAgfVxcbn1cXG4uYW5pbWF0ZS1wdWxzZSB7XFxuICBhbmltYXRpb246IHB1bHNlIDJzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGU7XFxufVxcbkBrZXlmcmFtZXMgc3BpbiB7XFxuXFxuICB0byB7XFxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XFxuICB9XFxufVxcbi5hbmltYXRlLXNwaW4ge1xcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcXG59XFxuLmN1cnNvci1ub3QtYWxsb3dlZCB7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXG4uY3Vyc29yLXBvaW50ZXIge1xcbiAgY3Vyc29yOiBwb2ludGVyO1xcbn1cXG4uc2VsZWN0LW5vbmUge1xcbiAgLXdlYmtpdC11c2VyLXNlbGVjdDogbm9uZTtcXG4gICAgIC1tb3otdXNlci1zZWxlY3Q6IG5vbmU7XFxuICAgICAgICAgIHVzZXItc2VsZWN0OiBub25lO1xcbn1cXG4ucmVzaXplLW5vbmUge1xcbiAgcmVzaXplOiBub25lO1xcbn1cXG4uZ3JpZC1jb2xzLTEge1xcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMSwgbWlubWF4KDAsIDFmcikpO1xcbn1cXG4uZ3JpZC1jb2xzLTIge1xcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgbWlubWF4KDAsIDFmcikpO1xcbn1cXG4uZmxleC1jb2wge1xcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcXG59XFxuLml0ZW1zLXN0YXJ0IHtcXG4gIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xcbn1cXG4uaXRlbXMtY2VudGVyIHtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxufVxcbi5pdGVtcy1iYXNlbGluZSB7XFxuICBhbGlnbi1pdGVtczogYmFzZWxpbmU7XFxufVxcbi5qdXN0aWZ5LWNlbnRlciB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcXG59XFxuLmp1c3RpZnktYmV0d2VlbiB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XFxufVxcbi5nYXAtMTIge1xcbiAgZ2FwOiAzcmVtO1xcbn1cXG4uZ2FwLTE2IHtcXG4gIGdhcDogNHJlbTtcXG59XFxuLmdhcC0yIHtcXG4gIGdhcDogMC41cmVtO1xcbn1cXG4uZ2FwLTMge1xcbiAgZ2FwOiAwLjc1cmVtO1xcbn1cXG4uZ2FwLTQge1xcbiAgZ2FwOiAxcmVtO1xcbn1cXG4uZ2FwLTYge1xcbiAgZ2FwOiAxLjVyZW07XFxufVxcbi5nYXAtOCB7XFxuICBnYXA6IDJyZW07XFxufVxcbi5zcGFjZS14LTEgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDAuMjVyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDAuMjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcbi5zcGFjZS14LTIgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDAuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpO1xcbiAgbWFyZ2luLWxlZnQ6IGNhbGMoMC41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteC0zID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygwLjc1cmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygwLjc1cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteC00ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygxcmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygxcmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteC04ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygycmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygycmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteS0zID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMC43NXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC43NXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS00ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS02ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMS41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygxLjVyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLnNwYWNlLXktOCA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS15LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tdG9wOiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLm92ZXJmbG93LWhpZGRlbiB7XFxuICBvdmVyZmxvdzogaGlkZGVuO1xcbn1cXG4ud2hpdGVzcGFjZS1ub3dyYXAge1xcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcXG59XFxuLnJvdW5kZWQge1xcbiAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcXG59XFxuLnJvdW5kZWQtMnhsIHtcXG4gIGJvcmRlci1yYWRpdXM6IDFyZW07XFxufVxcbi5yb3VuZGVkLTN4bCB7XFxuICBib3JkZXItcmFkaXVzOiAxLjVyZW07XFxufVxcbi5yb3VuZGVkLWZ1bGwge1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbn1cXG4ucm91bmRlZC1sZyB7XFxuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XFxufVxcbi5yb3VuZGVkLW1kIHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xcbn1cXG4ucm91bmRlZC14bCB7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbn1cXG4uYm9yZGVyIHtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbn1cXG4uYm9yZGVyLTIge1xcbiAgYm9yZGVyLXdpZHRoOiAycHg7XFxufVxcbi5ib3JkZXItdCB7XFxuICBib3JkZXItdG9wLXdpZHRoOiAxcHg7XFxufVxcbi5ib3JkZXItZ3JheS0yMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1ncmF5LTQwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTU2IDE2MyAxNzUgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLWdyYXktNjAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYig3NSA4NSA5OSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItd2hpdGVcXFxcLzEwIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4xKTtcXG59XFxuLmJvcmRlci10LXRyYW5zcGFyZW50IHtcXG4gIGJvcmRlci10b3AtY29sb3I6IHRyYW5zcGFyZW50O1xcbn1cXG4uYmctZ29sZC01MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0NSAxNTggMTEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1nb2xkLTYwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjE3IDExOSA2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS00MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDE1NiAxNjMgMTc1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS01MCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ5IDI1MCAyNTEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1wcmltYXJ5LTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDAgMjQ5IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXNlY29uZGFyeS01MCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjUzIDI0NCAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy10cmFuc3BhcmVudCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcXG59XFxuLmJnLXdoaXRlIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXdoaXRlXFxcXC8xMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjEpO1xcbn1cXG4uYmctd2hpdGVcXFxcLzUge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4wNSk7XFxufVxcbi5iZy13aGl0ZVxcXFwvOTUge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC45NSk7XFxufVxcbi5iZy1ncmFkaWVudC10by1iciB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tIHJpZ2h0LCB2YXIoLS10dy1ncmFkaWVudC1zdG9wcykpO1xcbn1cXG4uYmctZ3JhZGllbnQtdG8tciB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsIHZhcigtLXR3LWdyYWRpZW50LXN0b3BzKSk7XFxufVxcbi5mcm9tLWdvbGQtNTAwIHtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogI2Y1OWUwYiB2YXIoLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigyNDUgMTU4IDExIC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxuLmZyb20tcHJpbWFyeS01MCB7XFxuICAtLXR3LWdyYWRpZW50LWZyb206ICNmMGY5ZmYgdmFyKC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMjQwIDI0OSAyNTUgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXG4uZnJvbS1wcmltYXJ5LTUwMCB7XFxuICAtLXR3LWdyYWRpZW50LWZyb206ICMwZWE1ZTkgdmFyKC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMTQgMTY1IDIzMyAvIDApIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtc3RvcHM6IHZhcigtLXR3LWdyYWRpZW50LWZyb20pLCB2YXIoLS10dy1ncmFkaWVudC10byk7XFxufVxcbi5mcm9tLXB1cnBsZS01MDBcXFxcLzIwIHtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogcmdiKDE2OCA4NSAyNDcgLyAwLjIpIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDE2OCA4NSAyNDcgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXG4uZnJvbS10cmFuc3BhcmVudCB7XFxuICAtLXR3LWdyYWRpZW50LWZyb206IHRyYW5zcGFyZW50IHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDAgMCAwIC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxuLmZyb20teWVsbG93LTUwMFxcXFwvMjAge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiByZ2IoMjM0IDE3OSA4IC8gMC4yKSB2YXIoLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigyMzQgMTc5IDggLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXG4udmlhLWdvbGQtNjAwIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigyMTcgMTE5IDYgLyAwKSAgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksICNkOTc3MDYgdmFyKC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXG4udmlhLXdoaXRlXFxcXC8yMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMjU1IDI1NSAyNTUgLyAwKSAgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHJnYigyNTUgMjU1IDI1NSAvIDAuMikgdmFyKC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXG4udG8tYmx1ZS01MDBcXFxcLzIwIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYig1OSAxMzAgMjQ2IC8gMC4yKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcbi50by1vcmFuZ2UtNTAwXFxcXC8yMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMjQ5IDExNSAyMiAvIDAuMikgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXG4udG8tcHJpbWFyeS01MDAge1xcbiAgLS10dy1ncmFkaWVudC10bzogIzBlYTVlOSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcbi50by1zZWNvbmRhcnktNTAge1xcbiAgLS10dy1ncmFkaWVudC10bzogI2ZkZjRmZiB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcbi50by1zZWNvbmRhcnktNTAwIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86ICNkOTQ2ZWYgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXG4udG8tdHJhbnNwYXJlbnQge1xcbiAgLS10dy1ncmFkaWVudC10bzogdHJhbnNwYXJlbnQgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXG4ucC0xIHtcXG4gIHBhZGRpbmc6IDAuMjVyZW07XFxufVxcbi5wLTEyIHtcXG4gIHBhZGRpbmc6IDNyZW07XFxufVxcbi5wLTIge1xcbiAgcGFkZGluZzogMC41cmVtO1xcbn1cXG4ucC0zIHtcXG4gIHBhZGRpbmc6IDAuNzVyZW07XFxufVxcbi5wLTQge1xcbiAgcGFkZGluZzogMXJlbTtcXG59XFxuLnAtNiB7XFxuICBwYWRkaW5nOiAxLjVyZW07XFxufVxcbi5wLTgge1xcbiAgcGFkZGluZzogMnJlbTtcXG59XFxuLnB4LTEge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjI1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC4yNXJlbTtcXG59XFxuLnB4LTEwIHtcXG4gIHBhZGRpbmctbGVmdDogMi41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMi41cmVtO1xcbn1cXG4ucHgtMTIge1xcbiAgcGFkZGluZy1sZWZ0OiAzcmVtO1xcbiAgcGFkZGluZy1yaWdodDogM3JlbTtcXG59XFxuLnB4LTIge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjVyZW07XFxufVxcbi5weC00IHtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxufVxcbi5weC02IHtcXG4gIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbn1cXG4ucHgtOCB7XFxuICBwYWRkaW5nLWxlZnQ6IDJyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAycmVtO1xcbn1cXG4ucHktMFxcXFwuNSB7XFxuICBwYWRkaW5nLXRvcDogMC4xMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4xMjVyZW07XFxufVxcbi5weS0xIHtcXG4gIHBhZGRpbmctdG9wOiAwLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMjVyZW07XFxufVxcbi5weS0yIHtcXG4gIHBhZGRpbmctdG9wOiAwLjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xcbn1cXG4ucHktMjAge1xcbiAgcGFkZGluZy10b3A6IDVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogNXJlbTtcXG59XFxuLnB5LTMge1xcbiAgcGFkZGluZy10b3A6IDAuNzVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC43NXJlbTtcXG59XFxuLnB5LTQge1xcbiAgcGFkZGluZy10b3A6IDFyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcXG59XFxuLnB5LTUge1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMS4yNXJlbTtcXG59XFxuLnB5LTYge1xcbiAgcGFkZGluZy10b3A6IDEuNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxLjVyZW07XFxufVxcbi5weS04IHtcXG4gIHBhZGRpbmctdG9wOiAycmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDJyZW07XFxufVxcbi5weS0yXFxcXC41IHtcXG4gIHBhZGRpbmctdG9wOiAwLjYyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjYyNXJlbTtcXG59XFxuLnB0LTMge1xcbiAgcGFkZGluZy10b3A6IDAuNzVyZW07XFxufVxcbi5wdC00IHtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbn1cXG4udGV4dC1sZWZ0IHtcXG4gIHRleHQtYWxpZ246IGxlZnQ7XFxufVxcbi50ZXh0LWNlbnRlciB7XFxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XFxufVxcbi50ZXh0LXJpZ2h0IHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG4udGV4dC1zdGFydCB7XFxuICB0ZXh0LWFsaWduOiBzdGFydDtcXG59XFxuLmZvbnQtY2Fpcm8ge1xcbiAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtY2Fpcm8pLCBDYWlybywgVGFqYXdhbCwgTm90byBTYW5zIEFyYWJpYywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG4uZm9udC1kaXNwbGF5IHtcXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWRpc3BsYXkpLCBQb3BwaW5zLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxufVxcbi5mb250LXNhbnMge1xcbiAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtaW50ZXIpLCBJbnRlciwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG4uZm9udC10YWphd2FsIHtcXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LXRhamF3YWwpLCBUYWphd2FsLCBDYWlybywgTm90byBTYW5zIEFyYWJpYywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG4udGV4dC0yeGwge1xcbiAgZm9udC1zaXplOiAxLjVyZW07XFxuICBsaW5lLWhlaWdodDogMnJlbTtcXG59XFxuLnRleHQtM3hsIHtcXG4gIGZvbnQtc2l6ZTogMS44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMi4yNXJlbTtcXG59XFxuLnRleHQtNHhsIHtcXG4gIGZvbnQtc2l6ZTogMi4yNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAyLjVyZW07XFxufVxcbi50ZXh0LTV4bCB7XFxuICBmb250LXNpemU6IDNyZW07XFxuICBsaW5lLWhlaWdodDogMTtcXG59XFxuLnRleHQtYmFzZSB7XFxuICBmb250LXNpemU6IDFyZW07XFxuICBsaW5lLWhlaWdodDogMS41cmVtO1xcbn1cXG4udGV4dC1sZyB7XFxuICBmb250LXNpemU6IDEuMTI1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNzVyZW07XFxufVxcbi50ZXh0LXNtIHtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG59XFxuLnRleHQteGwge1xcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNzVyZW07XFxufVxcbi50ZXh0LXhzIHtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbn1cXG4uZm9udC1ibGFjayB7XFxuICBmb250LXdlaWdodDogOTAwO1xcbn1cXG4uZm9udC1ib2xkIHtcXG4gIGZvbnQtd2VpZ2h0OiA3MDA7XFxufVxcbi5mb250LW1lZGl1bSB7XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG4uZm9udC1zZW1pYm9sZCB7XFxuICBmb250LXdlaWdodDogNjAwO1xcbn1cXG4ubGVhZGluZy1yZWxheGVkIHtcXG4gIGxpbmUtaGVpZ2h0OiAxLjYyNTtcXG59XFxuLnRyYWNraW5nLXRpZ2h0IHtcXG4gIGxldHRlci1zcGFjaW5nOiAtMC4wMjVlbTtcXG59XFxuLnRleHQtZ3JheS0zMDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIwOSAyMTMgMjE5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktNDAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNTYgMTYzIDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmF5LTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNzUgODUgOTkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtZ3JheS03MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDU1IDY1IDgxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktOTAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNyAyNCAzOSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1wcmltYXJ5LTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMiAxMzIgMTk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXB1cnBsZS00MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE5MiAxMzIgMjUyIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC0zMDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1MiAxNjUgMTY1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC00MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI0OCAxMTMgMTEzIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXN1Y2Nlc3MtNTAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigzNCAxOTcgOTQgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtd2hpdGUge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXdoaXRlXFxcXC80MCB7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC40KTtcXG59XFxuLnRleHQtd2hpdGVcXFxcLzUwIHtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjUpO1xcbn1cXG4udGV4dC13aGl0ZVxcXFwvNjAge1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuNik7XFxufVxcbi50ZXh0LXdoaXRlXFxcXC83MCB7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC43KTtcXG59XFxuLnRleHQtd2hpdGVcXFxcLzgwIHtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjgpO1xcbn1cXG4udGV4dC13aGl0ZVxcXFwvOTAge1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuOSk7XFxufVxcbi50ZXh0LXdoaXRlXFxcXC85NSB7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC45NSk7XFxufVxcbi50ZXh0LXllbGxvdy00MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1MCAyMDQgMjEgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnVuZGVybGluZSB7XFxuICB0ZXh0LWRlY29yYXRpb24tbGluZTogdW5kZXJsaW5lO1xcbn1cXG4uZGVjb3JhdGlvbi13aGl0ZVxcXFwvMzAge1xcbiAgdGV4dC1kZWNvcmF0aW9uLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjMpO1xcbn1cXG4udW5kZXJsaW5lLW9mZnNldC00IHtcXG4gIHRleHQtdW5kZXJsaW5lLW9mZnNldDogNHB4O1xcbn1cXG4uYW50aWFsaWFzZWQge1xcbiAgLXdlYmtpdC1mb250LXNtb290aGluZzogYW50aWFsaWFzZWQ7XFxuICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlO1xcbn1cXG4ucGxhY2Vob2xkZXItZ3JheS01MDA6Oi1tb3otcGxhY2Vob2xkZXIge1xcbiAgLS10dy1wbGFjZWhvbGRlci1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxMDcgMTE0IDEyOCAvIHZhcigtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLnBsYWNlaG9sZGVyLWdyYXktNTAwOjpwbGFjZWhvbGRlciB7XFxuICAtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEwNyAxMTQgMTI4IC8gdmFyKC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4ub3BhY2l0eS0wIHtcXG4gIG9wYWNpdHk6IDA7XFxufVxcbi5vcGFjaXR5LTEwIHtcXG4gIG9wYWNpdHk6IDAuMTtcXG59XFxuLm9wYWNpdHktMTUge1xcbiAgb3BhY2l0eTogMC4xNTtcXG59XFxuLm9wYWNpdHktMjAge1xcbiAgb3BhY2l0eTogMC4yO1xcbn1cXG4ub3BhY2l0eS0yNSB7XFxuICBvcGFjaXR5OiAwLjI1O1xcbn1cXG4ub3BhY2l0eS0zMCB7XFxuICBvcGFjaXR5OiAwLjM7XFxufVxcbi5vcGFjaXR5LTQwIHtcXG4gIG9wYWNpdHk6IDAuNDtcXG59XFxuLm9wYWNpdHktNTAge1xcbiAgb3BhY2l0eTogMC41O1xcbn1cXG4uc2hhZG93IHtcXG4gIC0tdHctc2hhZG93OiAwIDFweCAzcHggMCByZ2IoMCAwIDAgLyAwLjEpLCAwIDFweCAycHggLTFweCByZ2IoMCAwIDAgLyAwLjEpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAxcHggM3B4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCAxcHggMnB4IC0xcHggdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcbi5zaGFkb3ctMnhsIHtcXG4gIC0tdHctc2hhZG93OiAwIDI1cHggNTBweCAtMTJweCByZ2IoMCAwIDAgLyAwLjI1KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMjVweCA1MHB4IC0xMnB4IHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG4uc2hhZG93LWxnIHtcXG4gIC0tdHctc2hhZG93OiAwIDEwcHggMTVweCAtM3B4IHJnYigwIDAgMCAvIDAuMSksIDAgNHB4IDZweCAtNHB4IHJnYigwIDAgMCAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDEwcHggMTVweCAtM3B4IHZhcigtLXR3LXNoYWRvdy1jb2xvciksIDAgNHB4IDZweCAtNHB4IHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG4ucmluZy0yIHtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxufVxcbi5yaW5nLXB1cnBsZS00MDAge1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDE7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYigxOTIgMTMyIDI1MiAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG4ucmluZy15ZWxsb3ctNDAwIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMjUwIDIwNCAyMSAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmx1ciB7XFxuICAtLXR3LWJsdXI6IGJsdXIoOHB4KTtcXG4gIGZpbHRlcjogdmFyKC0tdHctYmx1cikgdmFyKC0tdHctYnJpZ2h0bmVzcykgdmFyKC0tdHctY29udHJhc3QpIHZhcigtLXR3LWdyYXlzY2FsZSkgdmFyKC0tdHctaHVlLXJvdGF0ZSkgdmFyKC0tdHctaW52ZXJ0KSB2YXIoLS10dy1zYXR1cmF0ZSkgdmFyKC0tdHctc2VwaWEpIHZhcigtLXR3LWRyb3Atc2hhZG93KTtcXG59XFxuLmRyb3Atc2hhZG93IHtcXG4gIC0tdHctZHJvcC1zaGFkb3c6IGRyb3Atc2hhZG93KDAgMXB4IDJweCByZ2IoMCAwIDAgLyAwLjEpKSBkcm9wLXNoYWRvdygwIDFweCAxcHggcmdiKDAgMCAwIC8gMC4wNikpO1xcbiAgZmlsdGVyOiB2YXIoLS10dy1ibHVyKSB2YXIoLS10dy1icmlnaHRuZXNzKSB2YXIoLS10dy1jb250cmFzdCkgdmFyKC0tdHctZ3JheXNjYWxlKSB2YXIoLS10dy1odWUtcm90YXRlKSB2YXIoLS10dy1pbnZlcnQpIHZhcigtLXR3LXNhdHVyYXRlKSB2YXIoLS10dy1zZXBpYSkgdmFyKC0tdHctZHJvcC1zaGFkb3cpO1xcbn1cXG4uZHJvcC1zaGFkb3ctbGcge1xcbiAgLS10dy1kcm9wLXNoYWRvdzogZHJvcC1zaGFkb3coMCAxMHB4IDhweCByZ2IoMCAwIDAgLyAwLjA0KSkgZHJvcC1zaGFkb3coMCA0cHggM3B4IHJnYigwIDAgMCAvIDAuMSkpO1xcbiAgZmlsdGVyOiB2YXIoLS10dy1ibHVyKSB2YXIoLS10dy1icmlnaHRuZXNzKSB2YXIoLS10dy1jb250cmFzdCkgdmFyKC0tdHctZ3JheXNjYWxlKSB2YXIoLS10dy1odWUtcm90YXRlKSB2YXIoLS10dy1pbnZlcnQpIHZhcigtLXR3LXNhdHVyYXRlKSB2YXIoLS10dy1zZXBpYSkgdmFyKC0tdHctZHJvcC1zaGFkb3cpO1xcbn1cXG4uZ3JheXNjYWxlIHtcXG4gIC0tdHctZ3JheXNjYWxlOiBncmF5c2NhbGUoMTAwJSk7XFxuICBmaWx0ZXI6IHZhcigtLXR3LWJsdXIpIHZhcigtLXR3LWJyaWdodG5lc3MpIHZhcigtLXR3LWNvbnRyYXN0KSB2YXIoLS10dy1ncmF5c2NhbGUpIHZhcigtLXR3LWh1ZS1yb3RhdGUpIHZhcigtLXR3LWludmVydCkgdmFyKC0tdHctc2F0dXJhdGUpIHZhcigtLXR3LXNlcGlhKSB2YXIoLS10dy1kcm9wLXNoYWRvdyk7XFxufVxcbi5maWx0ZXIge1xcbiAgZmlsdGVyOiB2YXIoLS10dy1ibHVyKSB2YXIoLS10dy1icmlnaHRuZXNzKSB2YXIoLS10dy1jb250cmFzdCkgdmFyKC0tdHctZ3JheXNjYWxlKSB2YXIoLS10dy1odWUtcm90YXRlKSB2YXIoLS10dy1pbnZlcnQpIHZhcigtLXR3LXNhdHVyYXRlKSB2YXIoLS10dy1zZXBpYSkgdmFyKC0tdHctZHJvcC1zaGFkb3cpO1xcbn1cXG4uYmFja2Ryb3AtYmx1ci1tZCB7XFxuICAtLXR3LWJhY2tkcm9wLWJsdXI6IGJsdXIoMTJweCk7XFxuICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogdmFyKC0tdHctYmFja2Ryb3AtYmx1cikgdmFyKC0tdHctYmFja2Ryb3AtYnJpZ2h0bmVzcykgdmFyKC0tdHctYmFja2Ryb3AtY29udHJhc3QpIHZhcigtLXR3LWJhY2tkcm9wLWdyYXlzY2FsZSkgdmFyKC0tdHctYmFja2Ryb3AtaHVlLXJvdGF0ZSkgdmFyKC0tdHctYmFja2Ryb3AtaW52ZXJ0KSB2YXIoLS10dy1iYWNrZHJvcC1vcGFjaXR5KSB2YXIoLS10dy1iYWNrZHJvcC1zYXR1cmF0ZSkgdmFyKC0tdHctYmFja2Ryb3Atc2VwaWEpO1xcbiAgYmFja2Ryb3AtZmlsdGVyOiB2YXIoLS10dy1iYWNrZHJvcC1ibHVyKSB2YXIoLS10dy1iYWNrZHJvcC1icmlnaHRuZXNzKSB2YXIoLS10dy1iYWNrZHJvcC1jb250cmFzdCkgdmFyKC0tdHctYmFja2Ryb3AtZ3JheXNjYWxlKSB2YXIoLS10dy1iYWNrZHJvcC1odWUtcm90YXRlKSB2YXIoLS10dy1iYWNrZHJvcC1pbnZlcnQpIHZhcigtLXR3LWJhY2tkcm9wLW9wYWNpdHkpIHZhcigtLXR3LWJhY2tkcm9wLXNhdHVyYXRlKSB2YXIoLS10dy1iYWNrZHJvcC1zZXBpYSk7XFxufVxcbi50cmFuc2l0aW9uIHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGNvbG9yLCBiYWNrZ3JvdW5kLWNvbG9yLCBib3JkZXItY29sb3IsIHRleHQtZGVjb3JhdGlvbi1jb2xvciwgZmlsbCwgc3Ryb2tlLCBvcGFjaXR5LCBib3gtc2hhZG93LCB0cmFuc2Zvcm0sIGZpbHRlciwgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBjb2xvciwgYmFja2dyb3VuZC1jb2xvciwgYm9yZGVyLWNvbG9yLCB0ZXh0LWRlY29yYXRpb24tY29sb3IsIGZpbGwsIHN0cm9rZSwgb3BhY2l0eSwgYm94LXNoYWRvdywgdHJhbnNmb3JtLCBmaWx0ZXIsIGJhY2tkcm9wLWZpbHRlcjtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGNvbG9yLCBiYWNrZ3JvdW5kLWNvbG9yLCBib3JkZXItY29sb3IsIHRleHQtZGVjb3JhdGlvbi1jb2xvciwgZmlsbCwgc3Ryb2tlLCBvcGFjaXR5LCBib3gtc2hhZG93LCB0cmFuc2Zvcm0sIGZpbHRlciwgYmFja2Ryb3AtZmlsdGVyLCAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcbi50cmFuc2l0aW9uLWFsbCB7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi1jb2xvcnMge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogY29sb3IsIGJhY2tncm91bmQtY29sb3IsIGJvcmRlci1jb2xvciwgdGV4dC1kZWNvcmF0aW9uLWNvbG9yLCBmaWxsLCBzdHJva2U7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi1vcGFjaXR5IHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IG9wYWNpdHk7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi10cmFuc2Zvcm0ge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogdHJhbnNmb3JtO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxuLmR1cmF0aW9uLTEwMDAge1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTAwMG1zO1xcbn1cXG4uZHVyYXRpb24tMjAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXG4uZHVyYXRpb24tMzAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zO1xcbn1cXG4uZHVyYXRpb24tNTAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDUwMG1zO1xcbn1cXG4uZWFzZS1pbi1vdXQge1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxufVxcbi5ydGwge1xcbiAgZGlyZWN0aW9uOiBydGw7XFxufVxcbi5sdHIge1xcbiAgZGlyZWN0aW9uOiBsdHI7XFxufVxcbi8qIFRleHQgZGlyZWN0aW9uIHV0aWxpdGllcyAqL1xcbi50ZXh0LXN0YXJ0IHtcXG4gICAgdGV4dC1hbGlnbjogc3RhcnQ7XFxuICB9XFxuLyogTWFyZ2luIHV0aWxpdGllcyBmb3IgUlRMICovXFxuLyogUGFkZGluZyB1dGlsaXRpZXMgZm9yIFJUTCAqL1xcblxcbi8qIEltcG9ydCB0aGVtZSB1dGlsaXRpZXMgKi9cXG5cXG4vKiBDU1MgVmFyaWFibGVzIGZvciB0aGVtaW5nIC0gRW5oYW5jZWQgdGhlbWUgc3lzdGVtICovXFxuOnJvb3Qge1xcbiAgLyogTGVnYWN5IHRoZW1lIHZhcmlhYmxlcyAobWFpbnRhaW5lZCBmb3IgY29tcGF0aWJpbGl0eSkgKi9cXG4gIC0tcHJpbWFyeS1iZzogIzBmMTcyYTtcXG4gIC0tc2Vjb25kYXJ5LWJnOiAjMWUyOTNiO1xcbiAgLS1hY2NlbnQtY29sb3I6ICMwZWE1ZTk7XFxuICAtLXRleHQtcHJpbWFyeTogI2Y4ZmFmYztcXG4gIC0tdGV4dC1zZWNvbmRhcnk6ICNjYmQ1ZTE7XFxuICAtLWdsYXNzLWJnOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICAtLWdsYXNzLWJvcmRlcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgLS10b2FzdC1iZzogIzFmMjkzNztcXG4gIC0tdG9hc3QtY29sb3I6ICNmOWZhZmI7XFxuXFxuICAvKiBOZXcgdGhlbWUgc3lzdGVtIHZhcmlhYmxlcyAod2lsbCBiZSBvdmVycmlkZGVuIGJ5IHRoZW1lIHN3aXRjaGluZykgKi9cXG4gIC0tdGhlbWUtcHJpbWFyeS01MDA6ICNmNTllMGI7XFxuICAtLXRoZW1lLWdsYXNzLWJnOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjEyKTtcXG4gIC0tdGhlbWUtZ2xhc3MtYm9yZGVyOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjI1KTtcXG4gIC0tdGhlbWUtZ2xhc3Mtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMTUpO1xcbiAgLS10aGVtZS1nbGFzcy1ibHVyOiBibHVyKDI1cHgpO1xcbiAgLS10aGVtZS10ZXh0LXByaW1hcnk6ICNmZmZmZmY7XFxuICAtLXRoZW1lLXRleHQtc2Vjb25kYXJ5OiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XFxuICAtLXRoZW1lLXRleHQtYWNjZW50OiAjRkZENzAwO1xcbiAgLS10aGVtZS10ZXh0LW11dGVkOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNik7XFxuICAtLXRoZW1lLWdyYWRpZW50LXRleHQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzhCNjkxNCAwJSwgI0I4ODYwQiAxMCUsICNEQUE1MjAgMjAlLCAjRkZENzAwIDMwJSwgI0ZGRUQ0RSA0MCUsICNGRkY4REMgNTAlLCAjRkZFRDRFIDYwJSwgI0ZGRDcwMCA3MCUsICNEQUE1MjAgODAlLCAjQjg4NjBCIDkwJSwgIzhCNjkxNCAxMDAlKTtcXG4gIC0tdGhlbWUtZ3JhZGllbnQtYnV0dG9uOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRkZENzAwIDAlLCAjQjg4NjBCIDUwJSwgI0RBQTUyMCAxMDAlKTtcXG4gIC0tdGhlbWUtc2hhZG93LXByZW1pdW06IDAgMjVweCA1MHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7XFxufVxcblxcbltkYXRhLXRoZW1lPVxcXCJsaWdodFxcXCJdIHtcXG4gIC0tcHJpbWFyeS1iZzogI2ZmZmZmZjtcXG4gIC0tc2Vjb25kYXJ5LWJnOiAjZjhmYWZjO1xcbiAgLS1hY2NlbnQtY29sb3I6ICMwZWE1ZTk7XFxuICAtLXRleHQtcHJpbWFyeTogIzFmMjkzNztcXG4gIC0tdGV4dC1zZWNvbmRhcnk6ICM2NDc0OGI7XFxuICAtLWdsYXNzLWJnOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XFxuICAtLWdsYXNzLWJvcmRlcjogcmdiYSgwLCAwLCAwLCAwLjEpO1xcbiAgLS10b2FzdC1iZzogI2ZmZmZmZjtcXG4gIC0tdG9hc3QtY29sb3I6ICMxZjI5Mzc7XFxufVxcblxcbi8qIEJhc2Ugc3R5bGVzICovXFxuXFxuLyogQ29tcG9uZW50IHN0eWxlcyAqL1xcblxcbi8qIFV0aWxpdHkgY2xhc3NlcyAqL1xcblxcbi8qIEtleWZyYW1lIGFuaW1hdGlvbnMgKi9cXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7XFxuICB9XFxuICB0byB7XFxuICAgIG9wYWNpdHk6IDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBmYWRlSW5Eb3duIHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTMwcHgpO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgZmFkZUluTGVmdCB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0zMHB4KTtcXG4gIH1cXG4gIHRvIHtcXG4gICAgb3BhY2l0eTogMTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDApO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGZhZGVJblJpZ2h0IHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMzBweCk7XFxuICB9XFxuICB0byB7XFxuICAgIG9wYWNpdHk6IDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcXG4gIH1cXG59XFxuXFxuLyogRW5oYW5jZWQga2V5ZnJhbWUgYW5pbWF0aW9ucyBmb3IgZ2xhc3MgZWZmZWN0cyAqL1xcbkBrZXlmcmFtZXMgZ3JhZGllbnRTaGlmdCB7XFxuICAwJSwgMTAwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDAlIDUwJTsgfVxcbiAgNTAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSA1MCU7IH1cXG59XFxuXFxuQGtleWZyYW1lcyBwcmVtaXVtR3JhZGllbnQge1xcbiAgMCUsIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAwJTsgfVxcbiAgMjUlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSAwJTsgfVxcbiAgNTAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSAxMDAlOyB9XFxuICA3NSUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAxMDAlOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgZ2xhc3NTaGltbWVyIHtcXG4gIDAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogLTIwMCUgMDsgfVxcbiAgMTAwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDIwMCUgMDsgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGZsb2F0R2xhc3Mge1xcbiAgMCUsIDEwMCUge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMHB4KSByb3RhdGUoMGRlZyk7XFxuICAgIC8qIFJlbW92ZWQgYmx1ciBmaWx0ZXIgKi9cXG4gIH1cXG4gIDI1JSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCkgcm90YXRlKDFkZWcpO1xcbiAgICAvKiBSZW1vdmVkIGJsdXIgZmlsdGVyICovXFxuICB9XFxuICA1MCUge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTIwcHgpIHJvdGF0ZSgwZGVnKTtcXG4gICAgLyogUmVtb3ZlZCBibHVyIGZpbHRlciAqL1xcbiAgfVxcbiAgNzUlIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xMHB4KSByb3RhdGUoLTFkZWcpO1xcbiAgICAvKiBSZW1vdmVkIGJsdXIgZmlsdGVyICovXFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgcHVsc2VHbGFzcyB7XFxuICAwJSwgMTAwJSB7XFxuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XFxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XFxuICAgIGJveC1zaGFkb3c6IDAgMTJweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XFxuICB9XFxufVxcblxcbi8qIFByZW1pdW0gR29sZC9NZXRhbGxpYyBBbmltYXRpb25zICovXFxuQGtleWZyYW1lcyBnb2xkU2hpbmUge1xcbiAgMCUsIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSA1MCU7IH1cXG4gIDUwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgNTAlOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgcHJlbWl1bUdvbGRTaGluZSB7XFxuICAwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDAlOyB9XFxuICA1MCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAwJTsgfVxcbiAgMTAwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDIwMCUgMCU7IH1cXG59XFxuXFxuLyogU2hhcnAgYW5pbWF0aW9uIHdpdGhvdXQgYW55IGJsdXIgZWZmZWN0cyAqL1xcbkBrZXlmcmFtZXMgcHJlbWl1bUdvbGRTaGluZVNoYXJwIHtcXG4gIDAlIHtcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogLTIwMCUgMCU7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWigwKTtcXG4gIH1cXG4gIDUwJSB7XFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDAlIDAlO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVooMCk7XFxuICB9XFxuICAxMDAlIHtcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMjAwJSAwJTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGdvbGRHbGltbWVyIHtcXG4gIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0xMDAlKSBza2V3WCgtMTVkZWcpOyB9XFxuICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDIwMCUpIHNrZXdYKC0xNWRlZyk7IH1cXG59XFxuXFxuQGtleWZyYW1lcyB0ZXh0U2hpbW1lciB7XFxuICAwJSB7XFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDA7XFxuICAgIG9wYWNpdHk6IDA7XFxuICB9XFxuICA1MCUge1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAwO1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbiAgMTAwJSB7XFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDIwMCUgMDtcXG4gICAgb3BhY2l0eTogMDtcXG4gIH1cXG59XFxuXFxuXFxuXFxuQGtleWZyYW1lcyBnb2xkR2xvdyB7XFxuICAwJSwgMTAwJSB7XFxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpIHNjYWxlKDEpO1xcbiAgICBvcGFjaXR5OiAwLjM7XFxuICB9XFxuICA1MCUge1xcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpIHNjYWxlKDEuMSk7XFxuICAgIG9wYWNpdHk6IDAuNjtcXG4gIH1cXG59XFxuXFxuLyogUmVkdWNlZCBtb3Rpb24gcHJlZmVyZW5jZXMgKi9cXG5AbWVkaWEgKHByZWZlcnMtcmVkdWNlZC1tb3Rpb246IHJlZHVjZSkge1xcbiAgKixcXG4gICo6OmJlZm9yZSxcXG4gICo6OmFmdGVyIHtcXG4gICAgYW5pbWF0aW9uLWR1cmF0aW9uOiAwLjAxbXMgIWltcG9ydGFudDtcXG4gICAgYW5pbWF0aW9uLWl0ZXJhdGlvbi1jb3VudDogMSAhaW1wb3J0YW50O1xcbiAgICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAwLjAxbXMgIWltcG9ydGFudDtcXG4gICAgc2Nyb2xsLWJlaGF2aW9yOiBhdXRvICFpbXBvcnRhbnQ7XFxuICB9XFxufVxcblxcbi8qIFByaW50IHN0eWxlcyAqL1xcbkBtZWRpYSBwcmludCB7XFxuICAubm8tcHJpbnQge1xcbiAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XFxuICB9XFxuXFxuICAuZ2xhc3MsXFxuICAuZ2xhc3MtY2FyZCxcXG4gIC5nbGFzcy1idXR0b24ge1xcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZSAhaW1wb3J0YW50O1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IG5vbmUgIWltcG9ydGFudDtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IG5vbmUgIWltcG9ydGFudDtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2NjYyAhaW1wb3J0YW50O1xcbiAgfVxcbn1cXG4uaG92ZXJcXFxcOnNjYWxlLTEwNTpob3ZlciB7XFxuICAtLXR3LXNjYWxlLXg6IDEuMDU7XFxuICAtLXR3LXNjYWxlLXk6IDEuMDU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLmhvdmVyXFxcXDpiZy1ncmF5LTEwMDpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQzIDI0NCAyNDYgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6Ymctd2hpdGVcXFxcLzU6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4wNSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1wcmltYXJ5LTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMiAxMzIgMTk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1wcmltYXJ5LTcwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMyAxMDUgMTYxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC13aGl0ZTpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp1bmRlcmxpbmU6aG92ZXIge1xcbiAgdGV4dC1kZWNvcmF0aW9uLWxpbmU6IHVuZGVybGluZTtcXG59XFxuLmZvY3VzXFxcXDpvdXRsaW5lLW5vbmU6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG59XFxuLmZvY3VzXFxcXDpyaW5nLTI6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG59XFxuLmZvY3VzXFxcXDpyaW5nLXdoaXRlXFxcXC81MDpmb2N1cyB7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuNSk7XFxufVxcbi5ncm91cDpob3ZlciAuZ3JvdXAtaG92ZXJcXFxcOnRyYW5zbGF0ZS14LWZ1bGwge1xcbiAgLS10dy10cmFuc2xhdGUteDogMTAwJTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4uZ3JvdXA6aG92ZXIgLmdyb3VwLWhvdmVyXFxcXDpyb3RhdGUtMTIge1xcbiAgLS10dy1yb3RhdGU6IDEyZGVnO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi5ncm91cDpob3ZlciAuZ3JvdXAtaG92ZXJcXFxcOnNjYWxlLTEwNSB7XFxuICAtLXR3LXNjYWxlLXg6IDEuMDU7XFxuICAtLXR3LXNjYWxlLXk6IDEuMDU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLmdyb3VwOmhvdmVyIC5ncm91cC1ob3ZlclxcXFw6c2NhbGUtMTEwIHtcXG4gIC0tdHctc2NhbGUteDogMS4xO1xcbiAgLS10dy1zY2FsZS15OiAxLjE7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLmdyb3VwOmhvdmVyIC5ncm91cC1ob3ZlclxcXFw6c2NhbGUteC0xMDAge1xcbiAgLS10dy1zY2FsZS14OiAxO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi5ncm91cDpob3ZlciAuZ3JvdXAtaG92ZXJcXFxcOm9wYWNpdHktMTAwIHtcXG4gIG9wYWNpdHk6IDE7XFxufVxcbi5ncm91cDpob3ZlciAuZ3JvdXAtaG92ZXJcXFxcOm9wYWNpdHktMzAge1xcbiAgb3BhY2l0eTogMC4zO1xcbn1cXG4uZGFya1xcXFw6Ym9yZGVyLWdyYXktNjAwOmlzKC5kYXJrICopIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYig3NSA4NSA5OSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5kYXJrXFxcXDpib3JkZXItZ3JheS03MDA6aXMoLmRhcmsgKikge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDU1IDY1IDgxIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmRhcmtcXFxcOmJnLWdyYXktNjAwOmlzKC5kYXJrICopIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYig3NSA4NSA5OSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmRhcmtcXFxcOmJnLWdyYXktOTAwOmlzKC5kYXJrICopIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxNyAyNCAzOSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmRhcmtcXFxcOmJnLWdyYXktOTAwXFxcXC85NTppcyguZGFyayAqKSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTcgMjQgMzkgLyAwLjk1KTtcXG59XFxuLmRhcmtcXFxcOmJnLXByaW1hcnktOTAwXFxcXC8yMDppcyguZGFyayAqKSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTIgNzQgMTEwIC8gMC4yKTtcXG59XFxuLmRhcmtcXFxcOmJnLXNlY29uZGFyeS05MDBcXFxcLzIwOmlzKC5kYXJrICopIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxMTIgMjYgMTE3IC8gMC4yKTtcXG59XFxuLmRhcmtcXFxcOmZyb20tZ3JheS04MDA6aXMoLmRhcmsgKikge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiAjMWYyOTM3IHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDMxIDQxIDU1IC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxuLmRhcmtcXFxcOnRvLWdyYXktNzAwOmlzKC5kYXJrICopIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86ICMzNzQxNTEgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXG4uZGFya1xcXFw6dGV4dC1ncmF5LTMwMDppcyguZGFyayAqKSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmRhcmtcXFxcOnRleHQtZ3JheS00MDA6aXMoLmRhcmsgKikge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE1NiAxNjMgMTc1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5kYXJrXFxcXDp0ZXh0LWdyYXktNjAwOmlzKC5kYXJrICopIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig3NSA4NSA5OSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uZGFya1xcXFw6dGV4dC13aGl0ZTppcyguZGFyayAqKSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmRhcmtcXFxcOmhvdmVyXFxcXDpiZy1ncmF5LTgwMDpob3ZlcjppcyguZGFyayAqKSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzEgNDEgNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5kYXJrXFxcXDpob3ZlclxcXFw6dGV4dC1wcmltYXJ5LTQwMDpob3ZlcjppcyguZGFyayAqKSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNTYgMTg5IDI0OCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogNjQwcHgpIHtcXG5cXG4gIC5zbVxcXFw6dy1hdXRvIHtcXG4gICAgd2lkdGg6IGF1dG87XFxuICB9XFxuXFxuICAuc21cXFxcOmZsZXgtcm93IHtcXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcXG4gIH1cXG5cXG4gIC5zbVxcXFw6cHgtNiB7XFxuICAgIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICB9XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkge1xcblxcbiAgLm1kXFxcXDpncmlkLWNvbHMtMiB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6Z3JpZC1jb2xzLTMge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAubWRcXFxcOmZsZXgtcm93IHtcXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmxnXFxcXDpjb2wtc3Bhbi0yIHtcXG4gICAgZ3JpZC1jb2x1bW46IHNwYW4gMiAvIHNwYW4gMjtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6ZmxleCB7XFxuICAgIGRpc3BsYXk6IGZsZXg7XFxuICB9XFxuXFxuICAubGdcXFxcOmhpZGRlbiB7XFxuICAgIGRpc3BsYXk6IG5vbmU7XFxuICB9XFxuXFxuICAubGdcXFxcOmgtMTAge1xcbiAgICBoZWlnaHQ6IDIuNXJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6aC0yMCB7XFxuICAgIGhlaWdodDogNXJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6aC02IHtcXG4gICAgaGVpZ2h0OiAxLjVyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOnctMTAge1xcbiAgICB3aWR0aDogMi41cmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDp3LTYge1xcbiAgICB3aWR0aDogMS41cmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpzY2FsZS0xMTAge1xcbiAgICAtLXR3LXNjYWxlLXg6IDEuMTtcXG4gICAgLS10dy1zY2FsZS15OiAxLjE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpncmlkLWNvbHMtMiB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6Z3JpZC1jb2xzLTMge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAubGdcXFxcOmdyaWQtY29scy00IHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNCwgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpncmlkLWNvbHMtNSB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDUsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6cC0xMiB7XFxuICAgIHBhZGRpbmc6IDNyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOnAtMTYge1xcbiAgICBwYWRkaW5nOiA0cmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpweC04IHtcXG4gICAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAycmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpweS0zMiB7XFxuICAgIHBhZGRpbmctdG9wOiA4cmVtO1xcbiAgICBwYWRkaW5nLWJvdHRvbTogOHJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6dGV4dC0yeGwge1xcbiAgICBmb250LXNpemU6IDEuNXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDJyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOnRleHQtM3hsIHtcXG4gICAgZm9udC1zaXplOiAxLjg3NXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDIuMjVyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOnRleHQtNXhsIHtcXG4gICAgZm9udC1zaXplOiAzcmVtO1xcbiAgICBsaW5lLWhlaWdodDogMTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6dGV4dC02eGwge1xcbiAgICBmb250LXNpemU6IDMuNzVyZW07XFxuICAgIGxpbmUtaGVpZ2h0OiAxO1xcbiAgfVxcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogMTI4MHB4KSB7XFxuXFxuICAueGxcXFxcOnRleHQtNnhsIHtcXG4gICAgZm9udC1zaXplOiAzLjc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMTtcXG4gIH1cXG59XFxuLnJ0bFxcXFw6c3BhY2UteC1yZXZlcnNlOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS14LXJldmVyc2U6IDE7XFxufVxcblwiLCBcIlwiLHtcInZlcnNpb25cIjozLFwic291cmNlc1wiOltcIndlYnBhY2s6Ly9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzXCJdLFwibmFtZXNcIjpbXSxcIm1hcHBpbmdzXCI6XCJBQUFBO0VBQUEsd0JBQWM7RUFBZCx3QkFBYztFQUFkLG1CQUFjO0VBQWQsbUJBQWM7RUFBZCxjQUFjO0VBQWQsY0FBYztFQUFkLGNBQWM7RUFBZCxlQUFjO0VBQWQsZUFBYztFQUFkLGFBQWM7RUFBZCxhQUFjO0VBQWQsa0JBQWM7RUFBZCxzQ0FBYztFQUFkLDhCQUFjO0VBQWQsNkJBQWM7RUFBZCw0QkFBYztFQUFkLGVBQWM7RUFBZCxvQkFBYztFQUFkLHNCQUFjO0VBQWQsdUJBQWM7RUFBZCx3QkFBYztFQUFkLGtCQUFjO0VBQWQsMkJBQWM7RUFBZCw0QkFBYztFQUFkLHNDQUFjO0VBQWQsa0NBQWM7RUFBZCwyQkFBYztFQUFkLHNCQUFjO0VBQWQsOEJBQWM7RUFBZCxZQUFjO0VBQWQsa0JBQWM7RUFBZCxnQkFBYztFQUFkLGlCQUFjO0VBQWQsa0JBQWM7RUFBZCxjQUFjO0VBQWQsZ0JBQWM7RUFBZCxhQUFjO0VBQWQsbUJBQWM7RUFBZCxxQkFBYztFQUFkLDJCQUFjO0VBQWQseUJBQWM7RUFBZCwwQkFBYztFQUFkLDJCQUFjO0VBQWQsdUJBQWM7RUFBZCx3QkFBYztFQUFkLHlCQUFjO0VBQWQsc0JBQWM7RUFBZCxvQkFBYztFQUFkLHNCQUFjO0VBQWQscUJBQWM7RUFBZDtBQUFjOztBQUFkO0VBQUEsd0JBQWM7RUFBZCx3QkFBYztFQUFkLG1CQUFjO0VBQWQsbUJBQWM7RUFBZCxjQUFjO0VBQWQsY0FBYztFQUFkLGNBQWM7RUFBZCxlQUFjO0VBQWQsZUFBYztFQUFkLGFBQWM7RUFBZCxhQUFjO0VBQWQsa0JBQWM7RUFBZCxzQ0FBYztFQUFkLDhCQUFjO0VBQWQsNkJBQWM7RUFBZCw0QkFBYztFQUFkLGVBQWM7RUFBZCxvQkFBYztFQUFkLHNCQUFjO0VBQWQsdUJBQWM7RUFBZCx3QkFBYztFQUFkLGtCQUFjO0VBQWQsMkJBQWM7RUFBZCw0QkFBYztFQUFkLHNDQUFjO0VBQWQsa0NBQWM7RUFBZCwyQkFBYztFQUFkLHNCQUFjO0VBQWQsOEJBQWM7RUFBZCxZQUFjO0VBQWQsa0JBQWM7RUFBZCxnQkFBYztFQUFkLGlCQUFjO0VBQWQsa0JBQWM7RUFBZCxjQUFjO0VBQWQsZ0JBQWM7RUFBZCxhQUFjO0VBQWQsbUJBQWM7RUFBZCxxQkFBYztFQUFkLDJCQUFjO0VBQWQseUJBQWM7RUFBZCwwQkFBYztFQUFkLDJCQUFjO0VBQWQsdUJBQWM7RUFBZCx3QkFBYztFQUFkLHlCQUFjO0VBQWQsc0JBQWM7RUFBZCxvQkFBYztFQUFkLHNCQUFjO0VBQWQscUJBQWM7RUFBZDtBQUFjLENBQWQ7O0NBQWMsQ0FBZDs7O0NBQWM7O0FBQWQ7OztFQUFBLHNCQUFjLEVBQWQsTUFBYztFQUFkLGVBQWMsRUFBZCxNQUFjO0VBQWQsbUJBQWMsRUFBZCxNQUFjO0VBQWQscUJBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7O0VBQUEsZ0JBQWM7QUFBQTs7QUFBZDs7Ozs7Ozs7Q0FBYzs7QUFBZDs7RUFBQSxnQkFBYyxFQUFkLE1BQWM7RUFBZCw4QkFBYyxFQUFkLE1BQWM7RUFBZCxnQkFBYyxFQUFkLE1BQWM7RUFBZCxjQUFjO0tBQWQsV0FBYyxFQUFkLE1BQWM7RUFBZCw0REFBYyxFQUFkLE1BQWM7RUFBZCw2QkFBYyxFQUFkLE1BQWM7RUFBZCwrQkFBYyxFQUFkLE1BQWM7RUFBZCx3Q0FBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7O0NBQWM7O0FBQWQ7RUFBQSxTQUFjLEVBQWQsTUFBYztFQUFkLG9CQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOzs7O0NBQWM7O0FBQWQ7RUFBQSxTQUFjLEVBQWQsTUFBYztFQUFkLGNBQWMsRUFBZCxNQUFjO0VBQWQscUJBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7RUFBQSx5Q0FBYztVQUFkLGlDQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7Ozs7OztFQUFBLGtCQUFjO0VBQWQsb0JBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGNBQWM7RUFBZCx3QkFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkOztFQUFBLG1CQUFjO0FBQUE7O0FBQWQ7Ozs7O0NBQWM7O0FBQWQ7Ozs7RUFBQSxzQ0FBYyxFQUFkLE1BQWM7RUFBZCw2QkFBYyxFQUFkLE1BQWM7RUFBZCwrQkFBYyxFQUFkLE1BQWM7RUFBZCxjQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsY0FBYztBQUFBOztBQUFkOztDQUFjOztBQUFkOztFQUFBLGNBQWM7RUFBZCxjQUFjO0VBQWQsa0JBQWM7RUFBZCx3QkFBYztBQUFBOztBQUFkO0VBQUEsZUFBYztBQUFBOztBQUFkO0VBQUEsV0FBYztBQUFBOztBQUFkOzs7O0NBQWM7O0FBQWQ7RUFBQSxjQUFjLEVBQWQsTUFBYztFQUFkLHFCQUFjLEVBQWQsTUFBYztFQUFkLHlCQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOzs7O0NBQWM7O0FBQWQ7Ozs7O0VBQUEsb0JBQWMsRUFBZCxNQUFjO0VBQWQsOEJBQWMsRUFBZCxNQUFjO0VBQWQsZ0NBQWMsRUFBZCxNQUFjO0VBQWQsZUFBYyxFQUFkLE1BQWM7RUFBZCxvQkFBYyxFQUFkLE1BQWM7RUFBZCxvQkFBYyxFQUFkLE1BQWM7RUFBZCx1QkFBYyxFQUFkLE1BQWM7RUFBZCxjQUFjLEVBQWQsTUFBYztFQUFkLFNBQWMsRUFBZCxNQUFjO0VBQWQsVUFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7RUFBQSxvQkFBYztBQUFBOztBQUFkOzs7Q0FBYzs7QUFBZDs7OztFQUFBLDBCQUFjLEVBQWQsTUFBYztFQUFkLDZCQUFjLEVBQWQsTUFBYztFQUFkLHNCQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsYUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsZ0JBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLHdCQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7O0VBQUEsWUFBYztBQUFBOztBQUFkOzs7Q0FBYzs7QUFBZDtFQUFBLDZCQUFjLEVBQWQsTUFBYztFQUFkLG9CQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsd0JBQWM7QUFBQTs7QUFBZDs7O0NBQWM7O0FBQWQ7RUFBQSwwQkFBYyxFQUFkLE1BQWM7RUFBZCxhQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsa0JBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7Ozs7Ozs7Ozs7OztFQUFBLFNBQWM7QUFBQTs7QUFBZDtFQUFBLFNBQWM7RUFBZCxVQUFjO0FBQUE7O0FBQWQ7RUFBQSxVQUFjO0FBQUE7O0FBQWQ7OztFQUFBLGdCQUFjO0VBQWQsU0FBYztFQUFkLFVBQWM7QUFBQTs7QUFBZDs7Q0FBYztBQUFkO0VBQUEsVUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEsZ0JBQWM7QUFBQTs7QUFBZDs7O0NBQWM7O0FBQWQ7RUFBQSxVQUFjLEVBQWQsTUFBYztFQUFkLGNBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7O0VBQUEsVUFBYyxFQUFkLE1BQWM7RUFBZCxjQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkOztFQUFBLGVBQWM7QUFBQTs7QUFBZDs7Q0FBYztBQUFkO0VBQUEsZUFBYztBQUFBOztBQUFkOzs7O0NBQWM7O0FBQWQ7Ozs7Ozs7O0VBQUEsY0FBYyxFQUFkLE1BQWM7RUFBZCxzQkFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7RUFBQSxlQUFjO0VBQWQsWUFBYztBQUFBOztBQUFkLHdFQUFjO0FBQWQ7RUFBQSxhQUFjO0FBQUE7O0FBQWQ7RUFBQSx3QkFBYztLQUFkLHFCQUFjO1VBQWQsZ0JBQWM7RUFBZCxzQkFBYztFQUFkLHFCQUFjO0VBQWQsaUJBQWM7RUFBZCxrQkFBYztFQUFkLG1CQUFjO0VBQWQsc0JBQWM7RUFBZCxzQkFBYztFQUFkLHFCQUFjO0VBQWQsZUFBYztFQUFkLG1CQUFjO0VBQWQsc0JBQWM7QUFBQTs7QUFBZDtFQUFBLDhCQUFjO0VBQWQsbUJBQWM7RUFBZCw0Q0FBYztFQUFkLDJCQUFjO0VBQWQsNEJBQWM7RUFBZCx3QkFBYztFQUFkLDJHQUFjO0VBQWQseUdBQWM7RUFBZCxpRkFBYztFQUFkO0FBQWM7O0FBQWQ7RUFBQSxjQUFjO0VBQWQ7QUFBYzs7QUFBZDtFQUFBLGNBQWM7RUFBZDtBQUFjOztBQUFkO0VBQUE7QUFBYzs7QUFBZDtFQUFBLGlCQUFjO0VBQWQ7QUFBYzs7QUFBZDtFQUFBO0FBQWM7O0FBQWQ7RUFBQSxjQUFjO0VBQWQ7QUFBYzs7QUFBZDtFQUFBLG1QQUFjO0VBQWQsd0NBQWM7RUFBZCw0QkFBYztFQUFkLDRCQUFjO0VBQWQscUJBQWM7RUFBZCxpQ0FBYztVQUFkO0FBQWM7O0FBQWQ7RUFBQSx5QkFBYztFQUFkLDRCQUFjO0VBQWQsd0JBQWM7RUFBZCx3QkFBYztFQUFkLHNCQUFjO0VBQWQsaUNBQWM7VUFBZDtBQUFjOztBQUFkO0VBQUEsd0JBQWM7S0FBZCxxQkFBYztVQUFkLGdCQUFjO0VBQWQsVUFBYztFQUFkLGlDQUFjO1VBQWQseUJBQWM7RUFBZCxxQkFBYztFQUFkLHNCQUFjO0VBQWQsNkJBQWM7RUFBZCx5QkFBYztLQUFkLHNCQUFjO1VBQWQsaUJBQWM7RUFBZCxjQUFjO0VBQWQsWUFBYztFQUFkLFdBQWM7RUFBZCxjQUFjO0VBQWQsc0JBQWM7RUFBZCxxQkFBYztFQUFkLGlCQUFjO0VBQWQ7QUFBYzs7QUFBZDtFQUFBO0FBQWM7O0FBQWQ7RUFBQTtBQUFjOztBQUFkO0VBQUEsOEJBQWM7RUFBZCxtQkFBYztFQUFkLDRDQUFjO0VBQWQsMkJBQWM7RUFBZCw0QkFBYztFQUFkLHdCQUFjO0VBQWQsMkdBQWM7RUFBZCx5R0FBYztFQUFkO0FBQWM7O0FBQWQ7RUFBQSx5QkFBYztFQUFkLDhCQUFjO0VBQWQsMEJBQWM7RUFBZCwyQkFBYztFQUFkO0FBQWM7O0FBQWQ7RUFBQSxzUUFBYztBQUFBOztBQUFkOztFQUFBO0lBQUEsd0JBQWM7T0FBZCxxQkFBYztZQUFkO0VBQWM7QUFBQTs7QUFBZDtFQUFBLG9LQUFjO0FBQUE7O0FBQWQ7O0VBQUE7SUFBQSx3QkFBYztPQUFkLHFCQUFjO1lBQWQ7RUFBYztBQUFBOztBQUFkO0VBQUEseUJBQWM7RUFBZDtBQUFjOztBQUFkO0VBQUEsdU9BQWM7RUFBZCx5QkFBYztFQUFkLDhCQUFjO0VBQWQsMEJBQWM7RUFBZCwyQkFBYztFQUFkLDRCQUFjO0FBQUE7O0FBQWQ7O0VBQUE7SUFBQSx3QkFBYztPQUFkLHFCQUFjO1lBQWQ7RUFBYztBQUFBOztBQUFkO0VBQUEseUJBQWM7RUFBZDtBQUFjOztBQUFkO0VBQUEsaUJBQWM7RUFBZCxxQkFBYztFQUFkLGVBQWM7RUFBZCxnQkFBYztFQUFkLFVBQWM7RUFBZCxnQkFBYztFQUFkO0FBQWM7O0FBQWQ7RUFBQSw2QkFBYztFQUFkO0FBQWM7RUFBZDtJQUFBLHVCQUFjO0VBQUE7O0VBQWQ7RUFBQSxrQkFBYztFQUFkLHlEQUFjO0VBQWQsb0JBQWM7RUFBZCxtREFBYztFQUFkLCtGQUFjO0VBQWQsd0RBQWM7RUFBZCwwQkFBYztJQUFkLDZCQUFjO0lBQWQ7QUFBYzs7RUFBZCxzQ0FBYztFQUFkO0lBQUEsdUVBQWM7SUFBZCxtREFBYztJQUFkLGtDQUFjO0VBQUE7O0VBQWQ7SUFBQSw2RkFBYztJQUFkLDZEQUFjO0VBQUE7O0VBQWQ7SUFBQSwrRkFBYztJQUFkLG1EQUFjO0VBQUE7O0VBQWQsd0JBQWM7RUFBZDtJQUFBLGlCQUFjO0VBQUE7O0VBQWQscUJBQWM7RUFBZDtJQUFBLFVBQWM7RUFBQTs7RUFBZDtFQUFBLGtCQUFjO0VBQWQ7QUFBYzs7RUFBZDtFQUFBLGtCQUFjO0VBQWQ7QUFBYzs7RUFBZDtFQUFBLHFCQUFjO0VBQWQsa0JBQWM7RUFBZDtBQUFjOztFQUFkO0VBQUEsa0JBQWM7RUFBZDtBQUFjOztFQUFkO0VBQUEsa0JBQWM7RUFBZDtBQUFjOztFQUFkO0VBQUEsa0JBQWM7RUFBZDtBQUFjO0FBQ2Q7RUFBQTtBQUFvQjtBQUFwQjs7RUFBQTtJQUFBO0VBQW9CO0FBQUE7QUFBcEI7O0VBQUE7SUFBQTtFQUFvQjtBQUFBO0FBQXBCOztFQUFBO0lBQUE7RUFBb0I7QUFBQTtBQUFwQjs7RUFBQTtJQUFBO0VBQW9CO0FBQUE7QUFBcEI7O0VBQUE7SUFBQTtFQUFvQjtBQUFBO0FBQXBCO0VBQUEsb0NBQW9CO0VBQXBCLDJCQUFvQjtFQUFwQixtQ0FBb0I7RUFBcEIsMENBQW9CO0VBQXBCLHlDQUFvQjtFQUFwQjtBQUFvQjtBQUFwQjtFQUFBLHFDQUFvQjtFQUFwQiwyQkFBb0I7RUFBcEI7QUFBb0I7QUFBcEI7RUFBQSxvQ0FBb0I7RUFBcEIsMkJBQW9CO0VBQXBCLG1DQUFvQjtFQUFwQiwwQ0FBb0I7RUFBcEIsbUJBQW9CO0VBQXBCLGtCQUFvQjtFQUFwQixnQ0FBb0I7RUFBcEI7QUFBb0I7QUFBcEI7RUFBQSxvQ0FBb0I7RUFBcEIsdUNBQW9CO0VBQXBCO0FBQW9CO0FBQXBCO0VBQUEscUNBQW9CO0VBQXBCLDJCQUFvQjtFQUFwQixtQ0FBb0I7RUFBcEIsMkNBQW9CO0VBQXBCLG1CQUFvQjtFQUFwQixhQUFvQjtFQUFwQix5Q0FBb0I7RUFBcEI7QUFBb0I7QUFBcEI7RUFBQSxxQ0FBb0I7RUFBcEIsMkJBQW9CO0VBQXBCO0FBQW9CO0FBeUdsQixnREFBZ0Q7QUFFOUM7RUFBQSwrTEFBc1A7RUFBdFAsc0JBQXNQO0VBQXRQLHFFQUFzUDtFQUF0UCw0REFBc1A7RUFBdFAsbUVBQXNQO0VBQXRQLG1FQUFzUDtFQUF0UCx3REFBc1A7RUFBdFAsa0JBQXNQO0VBQXRQLG1CQUFzUDtFQUF0UCxpQkFBc1A7RUFBdFAsb0JBQXNQO0VBQXRQLGdCQUFzUDtFQUF0UCxvQkFBc1A7RUFBdFAsbURBQXNQO0VBQXRQLCtFQUFzUDtFQUF0UCxtR0FBc1A7RUFBdFAsdUdBQXNQO0VBQXRQLHdCQUFzUDtFQUF0UCx3REFBc1A7RUFBdFA7QUFBc1A7QUFBdFA7RUFBQSwwQkFBc1A7RUFBdFAsa0JBQXNQO0VBQXRQLGtCQUFzUDtFQUF0UCwrTEFBc1A7RUFBdFAsNERBQXNQO0VBQXRQLG1FQUFzUDtFQUF0UCxtRUFBc1A7RUFBdFAsd0RBQXNQO0VBQXRQLGdGQUFzUDtFQUF0UCxvR0FBc1A7RUFBdFA7QUFBc1A7QUFEeFA7SUFFRSxtQ0FBbUM7SUFDbkMsMkJBQTJCO0lBQzNCLDBDQUEwQztFQUM1QztBQUdFO0VBQUEsK0xBQWdQO0VBQWhQLHNCQUFnUDtFQUFoUCxpQkFBZ1A7RUFBaFAsb0NBQWdQO0VBQWhQLHdDQUFnUDtFQUFoUCxrQkFBZ1A7RUFBaFAsbUJBQWdQO0VBQWhQLGlCQUFnUDtFQUFoUCxvQkFBZ1A7RUFBaFAsZ0JBQWdQO0VBQWhQLG9CQUFnUDtFQUFoUCxpREFBZ1A7RUFBaFAsNkVBQWdQO0VBQWhQLGlHQUFnUDtFQUFoUCx1R0FBZ1A7RUFBaFAsd0JBQWdQO0VBQWhQLHdEQUFnUDtFQUFoUDtBQUFnUDtBQUFoUDtFQUFBLDBCQUFnUDtFQUFoUCwrTEFBZ1A7RUFBaFAsb0NBQWdQO0VBQWhQLHdDQUFnUDtFQUFoUCwrRUFBZ1A7RUFBaFAsbUdBQWdQO0VBQWhQO0FBQWdQO0FBQWhQO0VBQUEsb0JBQWdQO0VBQWhQO0FBQWdQO0FBRGxQO0lBRUUsbUNBQW1DO0lBQ25DLDJCQUEyQjtFQUM3QjtBQUdFO0VBQUEsc0JBQTBPO0VBQTFPLGlCQUEwTztFQUExTyxvQ0FBME87RUFBMU8seUNBQTBPO0VBQTFPLG9CQUEwTztFQUExTyxxQkFBME87RUFBMU8sb0JBQTBPO0VBQTFPLHVCQUEwTztFQUExTyxnQkFBME87RUFBMU8sb0JBQTBPO0VBQTFPLGdEQUEwTztFQUExTyx3QkFBME87RUFBMU8sd0RBQTBPO0VBQTFPO0FBQTBPO0FBQTFPO0VBQUEsb0NBQTBPO0VBQTFPO0FBQTBPO0FBQTFPO0VBQUEsaUNBQTBPO0VBQTFPLG9CQUEwTztFQUExTztBQUEwTztBQUExTztFQUFBO0FBQTBPO0FBRDVPO0lBRUUsbUNBQW1DO0lBQ25DLDJCQUEyQjtFQUM3QjtBQUVBLGtDQUFrQztBQUVoQztFQUFBLCtMQUE2SjtFQUE3SixzQkFBNko7RUFBN0osd0NBQTZKO0VBQTdKLGtCQUE2SjtFQUE3SixtQkFBNko7RUFBN0osaUJBQTZKO0VBQTdKLG9CQUE2SjtFQUE3SixnQkFBNko7RUFBN0osb0JBQTZKO0VBQTdKLG1EQUE2SjtFQUE3Six3QkFBNko7RUFBN0osd0RBQTZKO0VBQTdKO0FBQTZKO0FBQTdKO0VBQUEsMEJBQTZKO0VBQTdKLGtCQUE2SjtFQUE3SixrQkFBNko7RUFBN0osK0xBQTZKO0VBQTdKO0FBQTZKO0FBRC9KO0lBRUUsbUNBQW1DO0lBQ25DLDJCQUEyQjtJQUMzQiwwQ0FBMEM7SUFDMUMseUNBQXlDO0VBQzNDO0FBRUE7SUFDRSwyQ0FBMkM7RUFDN0M7QUFFQSwrQ0FBK0M7QUFxQy9DLGdEQUFnRDtBQUNoRDtJQUNFLGlHQUFpRztJQUNqRyxtQ0FBbUM7SUFDbkMsMkJBQTJCO0lBQzNCLDRDQUE0QztJQUM1QyxtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLGVBQWU7SUFDZixnQkFBZ0I7SUFDaEIsaURBQWlEO0lBQ2pELGtCQUFrQjtJQUNsQixnQkFBZ0I7SUFDaEIseUNBQXlDO0VBQzNDO0FBRUE7SUFDRSxXQUFXO0lBQ1gsa0JBQWtCO0lBQ2xCLE1BQU07SUFDTixXQUFXO0lBQ1gsV0FBVztJQUNYLFlBQVk7SUFDWixtR0FBbUc7SUFDbkcsMEJBQTBCO0VBQzVCO0FBRUE7SUFDRSxVQUFVO0VBQ1o7QUFFQTtJQUNFLHVDQUF1QztJQUN2QyxpR0FBaUc7SUFDakcsc0NBQXNDO0lBQ3RDLHFGQUFxRjtFQUN2RjtBQUVBLGdEQUFnRDtBQUNoRDtJQUNFLGlHQUFpRztJQUNqRyxtQ0FBbUM7SUFDbkMsMkJBQTJCO0lBQzNCLDJDQUEyQztJQUMzQyxtQkFBbUI7SUFDbkIseUNBQXlDO0lBQ3pDLGlEQUFpRDtFQUNuRDtBQUVBO0lBQ0UsaUdBQWlHO0lBQ2pHLHVDQUF1QztJQUN2QywyQ0FBMkM7SUFDM0MsdUNBQXVDO0VBQ3pDO0FBRUEsNENBQTRDO0FBMkIxQztFQUFBLHFCQUE4QztFQUE5Qyx3QkFBOEM7RUFBOUMsd0RBQThDO0VBQTlDLDBCQUE4QztJQUM5QyxnR0FBZ0c7SUFDaEcsbUNBQW1DO0lBQ25DLDJCQUEyQjtJQUMzQiwwQ0FBMEM7SUFDMUM7QUFMOEM7QUFRaEQ7SUFDRSx1Q0FBdUM7SUFDdkMsbUZBQW1GO0VBQ3JGO0FBRUEsaUJBQWlCO0FBS2pCLG9CQUFvQjtBQUVsQjtFQUFBLGlCQUFxQjtFQUFyQjtBQUFxQjtBQUFyQjs7RUFBQTtJQUFBLGlCQUFxQjtJQUFyQjtFQUFxQjtBQUFBO0FBSXJCO0VBQUEsa0JBQTJCO0VBQTNCO0FBQTJCO0FBQTNCOztFQUFBO0lBQUEsb0JBQTJCO0lBQTNCO0VBQTJCO0FBQUE7QUFBM0I7O0VBQUE7SUFBQSxrQkFBMkI7SUFBM0I7RUFBMkI7QUFBQTtBQUc3QixvRUFBb0U7QUFDcEU7SUFDRSxvQ0FBb0M7SUFDcEMsZ0JBQWdCO0lBQ2hCLGlCQUFpQjtJQUNqQix1QkFBdUI7SUFDdkIsNkZBQWlCO0lBQWpCLDZEQUFpQjtJQUFqQix1RkFBaUI7SUFDakIsMEVBQTBFO0lBQzFFLHFCQUFxQjtJQUNyQiw2QkFBNkI7SUFDN0Isb0NBQW9DO0lBQ3BDLDBDQUEwQztJQUMxQyxpREFBaUQ7RUFDbkQ7QUFlRTtFQUFBLGdFQUFpRTtFQUFqRSxtQkFBaUU7RUFBakUsb0JBQWlFO0VBQWpFLGdCQUFpRTtFQUFqRTtBQUFpRTtBQUFqRTs7RUFBQTtJQUFBLGtCQUFpRTtJQUFqRTtFQUFpRTtBQUFBO0FBSWpFO0VBQUEsaUJBQXdEO0VBQXhELGlCQUF3RDtFQUF4RCxnQkFBd0Q7RUFBeEQ7QUFBd0Q7QUFBeEQ7O0VBQUE7SUFBQSxtQkFBd0Q7SUFBeEQ7RUFBd0Q7QUFBQTtBQUl4RDtFQUFBLGtCQUF3QztFQUF4QyxvQkFBd0M7RUFBeEM7QUFBd0M7QUFBeEM7O0VBQUE7SUFBQSxpQkFBd0M7SUFBeEM7RUFBd0M7QUFBQTtBQUcxQyxtQ0FBbUM7QUFFakM7RUFBQSxxRUFBdUc7RUFBdkcsNERBQXVHO0VBQXZHLG1FQUF1RztFQUF2RyxtRUFBdUc7RUFBdkcscUVBQXVHO0VBQXZHLDRHQUF1RztFQUF2Ryx3REFBdUc7RUFBdkcsNkJBQXVHO1VBQXZHLHFCQUF1RztFQUF2RyxrQkFBdUc7SUFDdkcsMEJBQTBCO0lBQzFCO0FBRnVHO0FBZXpHLHVDQUF1QztBQUN2QztJQUNFLG9HQUFvRztJQUNwRywwQkFBMEI7SUFDMUIsNkJBQTZCO0lBQzdCLHFCQUFxQjtJQUNyQixvQ0FBb0M7SUFDcEMsNENBQTRDO0lBQzVDLDhDQUE4QztJQUM5QyxxREFBcUQ7RUFDdkQ7QUF5QkEsc0RBQXNEO0FBQ3REO0lBQ0U7Ozs7Ozs7Ozs7O21CQVdlO0lBQ2YsMEJBQTBCO0lBQzFCLDZCQUE2QjtJQUM3QixxQkFBcUI7SUFDckIsb0NBQW9DO0lBQ3BDLHdEQUF3RDtJQUN4RCx3Q0FBd0M7SUFDeEMsNkNBQTZDO0lBQzdDLHFEQUFxRDtJQUNyRCxrQkFBa0I7SUFDbEIsMENBQTBDO0lBQzFDLGtDQUFrQztJQUNsQyxtQ0FBbUM7SUFDbkMsa0NBQWtDO0lBQ2xDLHlDQUF5QztJQUN6Qyx3QkFBd0I7SUFDeEIsZ0NBQWdDO0lBQ2hDLDJCQUEyQjtFQUM3QjtBQVNBLCtCQUErQjtBQUU3QjtJQUFBLDZGQUFpQjtJQUFqQiw2REFBaUI7SUFBakIsdUZBQWlCO0lBQ2pCLHVFQUF1RTtJQUN2RSxtREFBbUQ7SUFDbkQsa0NBQWtDO0VBSGpCO0FBYWpCO0lBQUEsNkZBQWlCO0lBQWpCLDZEQUFpQjtJQUFqQix1RkFBaUI7SUFDakIsdUVBQXVFO0lBQ3ZFLG1EQUFtRDtJQUNuRCxrQ0FBa0M7SUFDbEMsMEJBQTBCO0lBQzFCLDZDQUE2QztJQUM3Qyx1QkFBdUI7SUFDdkIsZ0JBQWdCO0lBQ2hCLGdCQUFnQjtFQVJDO0FBV25CLDBEQUEwRDtBQVUxRCx5QkFBeUI7QUFPekIsaUNBQWlDO0FBaUJqQywwQkFBMEI7QUFtQjFCLHlDQUF5QztBQU16QywwQkFBMEI7QUFRMUIsbUNBQW1DO0FBVW5DLGtDQUFrQztBQUNsQztJQUNFOzs7Ozs7OzttQkFRZTtJQUNmLDBCQUEwQjtJQUMxQixtQ0FBbUM7SUFDbkMsMkJBQTJCO0lBQzNCLHdDQUF3QztJQUN4Qzs7Ozs0Q0FJd0M7SUFDeEMsY0FBYztJQUNkLGdCQUFnQjtJQUNoQiwrQ0FBK0M7SUFDL0MsbUJBQW1CO0lBQ25CLGtCQUFrQjtJQUNsQixnQ0FBZ0M7SUFDaEMsa0JBQWtCO0lBQ2xCLGdCQUFnQjtJQUNoQiw0Q0FBNEM7RUFDOUM7QUFFQTtJQUNFLFdBQVc7SUFDWCxrQkFBa0I7SUFDbEIsTUFBTTtJQUNOLFdBQVc7SUFDWCxXQUFXO0lBQ1gsWUFBWTtJQUNaLGtHQUFrRztJQUNsRywwQkFBMEI7RUFDNUI7QUFFQTtJQUNFLFVBQVU7RUFDWjtBQUVBO0lBQ0UsdUNBQXVDO0lBQ3ZDOzs7OzRDQUl3QztJQUN4Qyw4QkFBOEI7SUFDOUIscUNBQXFDO0lBQ3JDLG9DQUFvQztFQUN0QztBQUVBLDBCQUEwQjtBQUMxQjtJQUNFLDRGQUE0RjtJQUM1RixtQ0FBbUM7SUFDbkMsMkJBQTJCO0lBQzNCLHdDQUF3QztJQUN4QyxtQkFBbUI7SUFDbkIscUZBQXFGO0lBQ3JGLGdDQUFnQztJQUNoQyxrQkFBa0I7SUFDbEIsZ0JBQWdCO0VBQ2xCO0FBRUE7SUFDRSxXQUFXO0lBQ1gsa0JBQWtCO0lBQ2xCLFNBQVM7SUFDVCxVQUFVO0lBQ1YsV0FBVztJQUNYLFlBQVk7SUFDWiwrRUFBK0U7SUFDL0UsMkNBQTJDO0lBQzNDLG9CQUFvQjtFQUN0QjtBQUVBO0lBQ0UsdUNBQXVDO0lBQ3ZDLHNGQUFzRjtJQUN0RixvQ0FBb0M7RUFDdEM7QUFFQSx3QkFBd0I7QUEvbEIxQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQixrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxlQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5COztFQUFBO0lBQUE7RUFBbUI7QUFBQTtBQUFuQjtFQUFBO0FBQW1CO0FBQW5COztFQUFBO0lBQUE7RUFBbUI7QUFBQTtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHlCQUFtQjtLQUFuQixzQkFBbUI7VUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQix1REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsc0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLHVEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQixvREFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsb0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLCtEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQiw0REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsOERBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLDREQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSw0REFBbUI7RUFBbkIsb0VBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsNERBQW1CO0VBQW5CLHFFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDREQUFtQjtFQUFuQixvRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSwwRUFBbUI7RUFBbkIsb0VBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsZ0VBQW1CO0VBQW5CLCtEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHlFQUFtQjtFQUFuQixtRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxzRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHFCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHFCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHFCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsaUJBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsbUJBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsa0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsZUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxlQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsbUNBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsMkJBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsMkJBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLDBFQUFtQjtFQUFuQiw4RkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxnREFBbUI7RUFBbkIsNkRBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsK0VBQW1CO0VBQW5CLG1HQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDJHQUFtQjtFQUFuQix5R0FBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrR0FBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxtR0FBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSwrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLDhCQUFtQjtFQUFuQiwrUUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxnS0FBbUI7RUFBbkIsd0pBQW1CO0VBQW5CLGlMQUFtQjtFQUFuQix3REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx3QkFBbUI7RUFBbkIsd0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsK0ZBQW1CO0VBQW5CLHdEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDRCQUFtQjtFQUFuQix3REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSw4QkFBbUI7RUFBbkIsd0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFtbkJqQiw2QkFBNkI7QUFDN0I7SUFDRSxpQkFBaUI7RUFDbkI7QUFNQSw2QkFBNkI7QUFTN0IsOEJBQThCOztBQW5vQmhDLDJCQUEyQjs7QUFHM0Isc0RBQXNEO0FBQ3REO0VBQ0UsMERBQTBEO0VBQzFELHFCQUFxQjtFQUNyQix1QkFBdUI7RUFDdkIsdUJBQXVCO0VBQ3ZCLHVCQUF1QjtFQUN2Qix5QkFBeUI7RUFDekIsb0NBQW9DO0VBQ3BDLHdDQUF3QztFQUN4QyxtQkFBbUI7RUFDbkIsc0JBQXNCOztFQUV0Qix1RUFBdUU7RUFDdkUsNEJBQTRCO0VBQzVCLHlDQUF5QztFQUN6Qyw2Q0FBNkM7RUFDN0Msd0RBQXdEO0VBQ3hELDhCQUE4QjtFQUM5Qiw2QkFBNkI7RUFDN0IsZ0RBQWdEO0VBQ2hELDRCQUE0QjtFQUM1Qiw0Q0FBNEM7RUFDNUMsNExBQTRMO0VBQzVMLHVGQUF1RjtFQUN2RiwwREFBMEQ7QUFDNUQ7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsdUJBQXVCO0VBQ3ZCLHVCQUF1QjtFQUN2Qix1QkFBdUI7RUFDdkIseUJBQXlCO0VBQ3pCLG9DQUFvQztFQUNwQyxrQ0FBa0M7RUFDbEMsbUJBQW1CO0VBQ25CLHNCQUFzQjtBQUN4Qjs7QUFFQSxnQkFBZ0I7O0FBeURoQixxQkFBcUI7O0FBMmdCckIsb0JBQW9COztBQThCcEIsd0JBQXdCO0FBQ3hCO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsMkJBQTJCO0VBQzdCO0VBQ0E7SUFDRSxVQUFVO0lBQ1Ysd0JBQXdCO0VBQzFCO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLFVBQVU7SUFDViw0QkFBNEI7RUFDOUI7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQTtFQUNFO0lBQ0UsVUFBVTtJQUNWLDRCQUE0QjtFQUM5QjtFQUNBO0lBQ0UsVUFBVTtJQUNWLHdCQUF3QjtFQUMxQjtBQUNGOztBQUVBO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsMkJBQTJCO0VBQzdCO0VBQ0E7SUFDRSxVQUFVO0lBQ1Ysd0JBQXdCO0VBQzFCO0FBQ0Y7O0FBRUEsbURBQW1EO0FBQ25EO0VBQ0UsV0FBVywyQkFBMkIsRUFBRTtFQUN4QyxNQUFNLDZCQUE2QixFQUFFO0FBQ3ZDOztBQUVBO0VBQ0UsV0FBVywwQkFBMEIsRUFBRTtFQUN2QyxNQUFNLDRCQUE0QixFQUFFO0VBQ3BDLE1BQU0sOEJBQThCLEVBQUU7RUFDdEMsTUFBTSw0QkFBNEIsRUFBRTtBQUN0Qzs7QUFFQTtFQUNFLEtBQUssNEJBQTRCLEVBQUU7RUFDbkMsT0FBTywyQkFBMkIsRUFBRTtBQUN0Qzs7QUFFQTtFQUNFO0lBQ0UsdUNBQXVDO0lBQ3ZDLHdCQUF3QjtFQUMxQjtFQUNBO0lBQ0UseUNBQXlDO0lBQ3pDLHdCQUF3QjtFQUMxQjtFQUNBO0lBQ0UseUNBQXlDO0lBQ3pDLHdCQUF3QjtFQUMxQjtFQUNBO0lBQ0UsMENBQTBDO0lBQzFDLHdCQUF3QjtFQUMxQjtBQUNGOztBQUVBO0VBQ0U7SUFDRSxvQ0FBb0M7SUFDcEMsc0NBQXNDO0lBQ3RDLHlDQUF5QztFQUMzQztFQUNBO0lBQ0UscUNBQXFDO0lBQ3JDLHNDQUFzQztJQUN0QywyQ0FBMkM7RUFDN0M7QUFDRjs7QUFFQSxxQ0FBcUM7QUFDckM7RUFDRSxXQUFXLDJCQUEyQixFQUFFO0VBQ3hDLE1BQU0sNkJBQTZCLEVBQUU7QUFDdkM7O0FBRUE7RUFDRSxLQUFLLDZCQUE2QixFQUFFO0VBQ3BDLE1BQU0sMEJBQTBCLEVBQUU7RUFDbEMsT0FBTyw0QkFBNEIsRUFBRTtBQUN2Qzs7QUFFQSw2Q0FBNkM7QUFDN0M7RUFDRTtJQUNFLDZCQUE2QjtJQUM3Qix3QkFBd0I7RUFDMUI7RUFDQTtJQUNFLDBCQUEwQjtJQUMxQix3QkFBd0I7RUFDMUI7RUFDQTtJQUNFLDRCQUE0QjtJQUM1Qix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQTtFQUNFLEtBQUssMENBQTBDLEVBQUU7RUFDakQsT0FBTyx5Q0FBeUMsRUFBRTtBQUNwRDs7QUFFQTtFQUNFO0lBQ0UsNEJBQTRCO0lBQzVCLFVBQVU7RUFDWjtFQUNBO0lBQ0UseUJBQXlCO0lBQ3pCLFVBQVU7RUFDWjtFQUNBO0lBQ0UsMkJBQTJCO0lBQzNCLFVBQVU7RUFDWjtBQUNGOzs7O0FBSUE7RUFDRTtJQUNFLGdDQUFnQztJQUNoQyxZQUFZO0VBQ2Q7RUFDQTtJQUNFLG9DQUFvQztJQUNwQyxZQUFZO0VBQ2Q7QUFDRjs7QUFFQSwrQkFBK0I7QUFDL0I7RUFDRTs7O0lBR0UscUNBQXFDO0lBQ3JDLHVDQUF1QztJQUN2QyxzQ0FBc0M7SUFDdEMsZ0NBQWdDO0VBQ2xDO0FBQ0Y7O0FBRUEsaUJBQWlCO0FBQ2pCO0VBQ0U7SUFDRSx3QkFBd0I7RUFDMUI7O0VBRUE7OztJQUdFLDRCQUE0QjtJQUM1QixnQ0FBZ0M7SUFDaEMsd0NBQXdDO0lBQ3hDLGlDQUFpQztFQUNuQztBQUNGO0FBdjBCQTtFQUFBLGtCQXcwQkE7RUF4MEJBLGtCQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7RUFBQSxrQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUE7QUF3MEJBO0FBeDBCQTtFQUFBLG9CQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7RUFBQSxvQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsb0JBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBO0FBdzBCQTtBQXgwQkE7RUFBQSw4QkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsMkdBdzBCQTtFQXgwQkEseUdBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBO0FBdzBCQTtBQXgwQkE7RUFBQSxzQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsa0JBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBLGtCQXcwQkE7RUF4MEJBLGtCQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7RUFBQSxpQkF3MEJBO0VBeDBCQSxpQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsZUF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUE7QUF3MEJBO0FBeDBCQTtFQUFBO0FBdzBCQTtBQXgwQkE7RUFBQSxzQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsc0JBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBLGtCQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7RUFBQSxrQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUE7QUF3MEJBO0FBeDBCQTtFQUFBO0FBdzBCQTtBQXgwQkE7RUFBQTtBQXcwQkE7QUF4MEJBO0VBQUEsNERBdzBCQTtFQXgwQkEsa0VBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBO0FBdzBCQTtBQXgwQkE7RUFBQSxvQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsb0JBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBLG9CQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7RUFBQSxvQkF3MEJBO0VBeDBCQTtBQXcwQkE7QUF4MEJBO0VBQUEsa0JBdzBCQTtFQXgwQkE7QUF3MEJBO0FBeDBCQTtFQUFBLG9CQXcwQkE7RUF4MEJBO0FBdzBCQTtBQXgwQkE7O0VBQUE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUEsb0JBdzBCQTtJQXgwQkE7RUF3MEJBO0FBQUE7QUF4MEJBOztFQUFBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTtBQUFBO0FBeDBCQTs7RUFBQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQSxpQkF3MEJBO0lBeDBCQSxpQkF3MEJBO0lBeDBCQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBO0VBdzBCQTs7RUF4MEJBO0lBQUE7RUF3MEJBOztFQXgwQkE7SUFBQTtFQXcwQkE7O0VBeDBCQTtJQUFBLGtCQXcwQkE7SUF4MEJBO0VBdzBCQTs7RUF4MEJBO0lBQUEsaUJBdzBCQTtJQXgwQkE7RUF3MEJBOztFQXgwQkE7SUFBQSxpQkF3MEJBO0lBeDBCQTtFQXcwQkE7O0VBeDBCQTtJQUFBLG1CQXcwQkE7SUF4MEJBO0VBdzBCQTs7RUF4MEJBO0lBQUEsZUF3MEJBO0lBeDBCQTtFQXcwQkE7O0VBeDBCQTtJQUFBLGtCQXcwQkE7SUF4MEJBO0VBdzBCQTtBQUFBO0FBeDBCQTs7RUFBQTtJQUFBLGtCQXcwQkE7SUF4MEJBO0VBdzBCQTtBQUFBO0FBeDBCQTtFQUFBO0FBdzBCQVwiLFwic291cmNlc0NvbnRlbnRcIjpbXCJAdGFpbHdpbmQgYmFzZTtcXG5AdGFpbHdpbmQgY29tcG9uZW50cztcXG5AdGFpbHdpbmQgdXRpbGl0aWVzO1xcblxcbi8qIEltcG9ydCB0aGVtZSB1dGlsaXRpZXMgKi9cXG5AaW1wb3J0ICcuL3RoZW1lcy90aGVtZS1iYXNlLmNzcyc7XFxuXFxuLyogQ1NTIFZhcmlhYmxlcyBmb3IgdGhlbWluZyAtIEVuaGFuY2VkIHRoZW1lIHN5c3RlbSAqL1xcbjpyb290IHtcXG4gIC8qIExlZ2FjeSB0aGVtZSB2YXJpYWJsZXMgKG1haW50YWluZWQgZm9yIGNvbXBhdGliaWxpdHkpICovXFxuICAtLXByaW1hcnktYmc6ICMwZjE3MmE7XFxuICAtLXNlY29uZGFyeS1iZzogIzFlMjkzYjtcXG4gIC0tYWNjZW50LWNvbG9yOiAjMGVhNWU5O1xcbiAgLS10ZXh0LXByaW1hcnk6ICNmOGZhZmM7XFxuICAtLXRleHQtc2Vjb25kYXJ5OiAjY2JkNWUxO1xcbiAgLS1nbGFzcy1iZzogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbiAgLS1nbGFzcy1ib3JkZXI6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIC0tdG9hc3QtYmc6ICMxZjI5Mzc7XFxuICAtLXRvYXN0LWNvbG9yOiAjZjlmYWZiO1xcblxcbiAgLyogTmV3IHRoZW1lIHN5c3RlbSB2YXJpYWJsZXMgKHdpbGwgYmUgb3ZlcnJpZGRlbiBieSB0aGVtZSBzd2l0Y2hpbmcpICovXFxuICAtLXRoZW1lLXByaW1hcnktNTAwOiAjZjU5ZTBiO1xcbiAgLS10aGVtZS1nbGFzcy1iZzogcmdiYSgyNTUsIDIxNSwgMCwgMC4xMik7XFxuICAtLXRoZW1lLWdsYXNzLWJvcmRlcjogcmdiYSgyNTUsIDIxNSwgMCwgMC4yNSk7XFxuICAtLXRoZW1lLWdsYXNzLXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjE1KTtcXG4gIC0tdGhlbWUtZ2xhc3MtYmx1cjogYmx1cigyNXB4KTtcXG4gIC0tdGhlbWUtdGV4dC1wcmltYXJ5OiAjZmZmZmZmO1xcbiAgLS10aGVtZS10ZXh0LXNlY29uZGFyeTogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xcbiAgLS10aGVtZS10ZXh0LWFjY2VudDogI0ZGRDcwMDtcXG4gIC0tdGhlbWUtdGV4dC1tdXRlZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpO1xcbiAgLS10aGVtZS1ncmFkaWVudC10ZXh0OiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM4QjY5MTQgMCUsICNCODg2MEIgMTAlLCAjREFBNTIwIDIwJSwgI0ZGRDcwMCAzMCUsICNGRkVENEUgNDAlLCAjRkZGOERDIDUwJSwgI0ZGRUQ0RSA2MCUsICNGRkQ3MDAgNzAlLCAjREFBNTIwIDgwJSwgI0I4ODYwQiA5MCUsICM4QjY5MTQgMTAwJSk7XFxuICAtLXRoZW1lLWdyYWRpZW50LWJ1dHRvbjogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0ZGRDcwMCAwJSwgI0I4ODYwQiA1MCUsICNEQUE1MjAgMTAwJSk7XFxuICAtLXRoZW1lLXNoYWRvdy1wcmVtaXVtOiAwIDI1cHggNTBweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpO1xcbn1cXG5cXG5bZGF0YS10aGVtZT1cXFwibGlnaHRcXFwiXSB7XFxuICAtLXByaW1hcnktYmc6ICNmZmZmZmY7XFxuICAtLXNlY29uZGFyeS1iZzogI2Y4ZmFmYztcXG4gIC0tYWNjZW50LWNvbG9yOiAjMGVhNWU5O1xcbiAgLS10ZXh0LXByaW1hcnk6ICMxZjI5Mzc7XFxuICAtLXRleHQtc2Vjb25kYXJ5OiAjNjQ3NDhiO1xcbiAgLS1nbGFzcy1iZzogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xcbiAgLS1nbGFzcy1ib3JkZXI6IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIC0tdG9hc3QtYmc6ICNmZmZmZmY7XFxuICAtLXRvYXN0LWNvbG9yOiAjMWYyOTM3O1xcbn1cXG5cXG4vKiBCYXNlIHN0eWxlcyAqL1xcbkBsYXllciBiYXNlIHtcXG4gIGh0bWwge1xcbiAgICBzY3JvbGwtYmVoYXZpb3I6IHNtb290aDtcXG4gIH1cXG4gIFxcbiAgYm9keSB7XFxuICAgIEBhcHBseSBiZy1kYXJrLTkwMCB0ZXh0LWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMDtcXG4gICAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS1iZyk7XFxuICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xcbiAgfVxcbiAgXFxuICAvKiBFbmhhbmNlZCBBcmFiaWMgZm9udCBvcHRpbWl6YXRpb24gKi9cXG4gIC5mb250LWFyYWJpYyB7XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMSwgXFxcInNzMDFcXFwiIDEsIFxcXCJzczAyXFxcIiAxO1xcbiAgICBmb250LXZhcmlhbnQtbGlnYXR1cmVzOiBjb21tb24tbGlnYXR1cmVzIGNvbnRleHR1YWw7XFxuICAgIHRleHQtcmVuZGVyaW5nOiBvcHRpbWl6ZUxlZ2liaWxpdHk7XFxuICB9XFxuXFxuICAuZm9udC1jYWlybyB7XFxuICAgIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWNhaXJvKSwgJ0NhaXJvJywgJ1RhamF3YWwnLCAnTm90byBTYW5zIEFyYWJpYycsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjtcXG4gICAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBcXFwia2VyblxcXCIgMSwgXFxcImxpZ2FcXFwiIDEsIFxcXCJjYWx0XFxcIiAxLCBcXFwic3MwMVxcXCIgMTtcXG4gIH1cXG5cXG4gIC5mb250LXRhamF3YWwge1xcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC10YWphd2FsKSwgJ1RhamF3YWwnLCAnQ2Fpcm8nLCAnTm90byBTYW5zIEFyYWJpYycsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjtcXG4gICAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBcXFwia2VyblxcXCIgMSwgXFxcImxpZ2FcXFwiIDEsIFxcXCJjYWx0XFxcIiAxO1xcbiAgfVxcbiAgXFxuICAvKiBSVEwgc3BlY2lmaWMgc3R5bGVzICovXFxuICBbZGlyPVxcXCJydGxcXFwiXSB7XFxuICAgIHRleHQtYWxpZ246IHJpZ2h0O1xcbiAgfVxcbiAgXFxuICBbZGlyPVxcXCJydGxcXFwiXSAubHRyLWNvbnRlbnQge1xcbiAgICBkaXJlY3Rpb246IGx0cjtcXG4gICAgdGV4dC1hbGlnbjogbGVmdDtcXG4gIH1cXG4gIFxcbiAgLyogQ3VzdG9tIHNjcm9sbGJhciAqL1xcbiAgOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICAgIHdpZHRoOiA4cHg7XFxuICB9XFxuICBcXG4gIDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgICBAYXBwbHkgYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTgwMDtcXG4gIH1cXG4gIFxcbiAgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxuICAgIEBhcHBseSBiZy1ncmF5LTMwMCBkYXJrOmJnLWdyYXktNjAwIHJvdW5kZWQtZnVsbDtcXG4gIH1cXG4gIFxcbiAgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxuICAgIEBhcHBseSBiZy1ncmF5LTQwMCBkYXJrOmJnLWdyYXktNTAwO1xcbiAgfVxcbn1cXG5cXG4vKiBDb21wb25lbnQgc3R5bGVzICovXFxuQGxheWVyIGNvbXBvbmVudHMge1xcbiAgLyogRW5oYW5jZWQgQnV0dG9uIHZhcmlhbnRzIHdpdGggZ2xhc3MgZWZmZWN0cyAqL1xcbiAgLmJ0bi1wcmltYXJ5IHtcXG4gICAgQGFwcGx5IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5LTYwMCB0by1wcmltYXJ5LTcwMCBob3Zlcjpmcm9tLXByaW1hcnktNzAwIGhvdmVyOnRvLXByaW1hcnktODAwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweC04IHB5LTQgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2Zvcm0gaG92ZXI6LXRyYW5zbGF0ZS15LTEgaG92ZXI6c2NhbGUtMTA1O1xcbiAgICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICB9XFxuXFxuICAuYnRuLXNlY29uZGFyeSB7XFxuICAgIEBhcHBseSBiZy13aGl0ZS8xMCBob3ZlcjpiZy13aGl0ZS8yMCB0ZXh0LXByaW1hcnktNjAwIGRhcms6dGV4dC1wcmltYXJ5LTQwMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIGhvdmVyOmJvcmRlci13aGl0ZS8zMCBmb250LXNlbWlib2xkIHB4LTggcHktNCByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gIH1cXG5cXG4gIC5idG4tb3V0bGluZSB7XFxuICAgIEBhcHBseSBib3JkZXIgYm9yZGVyLWdyYXktMzAwLzMwIGhvdmVyOmJvcmRlci1ncmF5LTQwMC81MCB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMC8zMCBkYXJrOmhvdmVyOmJvcmRlci1ncmF5LTUwMC81MCBmb250LW1lZGl1bSBweC02IHB5LTMgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYmctd2hpdGUvNSBob3ZlcjpiZy13aGl0ZS8xMDtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcXG4gIH1cXG5cXG4gIC8qIEVuaGFuY2VkIEdsYXNzIGVmZmVjdCBidXR0b25zICovXFxuICAuYnRuLWdsYXNzIHtcXG4gICAgQGFwcGx5IGJnLXdoaXRlLzEwIGhvdmVyOmJnLXdoaXRlLzIwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweC04IHB5LTQgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0xIGhvdmVyOnNjYWxlLTEwNTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG5cXG4gIC5idG4tZ2xhc3M6aG92ZXIge1xcbiAgICBib3gtc2hhZG93OiAwIDEycHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xcbiAgfVxcblxcbiAgLyogRW5oYW5jZWQgZ2xhc3MgYnV0dG9uIHdpdGggcHJlbWl1bSBlZmZlY3RzICovXFxuICAuZ2xhc3MtYnV0dG9uLWVuaGFuY2VkIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KSAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMzJweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigzMnB4KTtcXG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KTtcXG4gICAgYm9yZGVyLXJhZGl1czogMTZweDtcXG4gICAgcGFkZGluZzogMS4yNXJlbSAyLjVyZW07XFxuICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XFxuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcXG4gIH1cXG5cXG4gIC5nbGFzcy1idXR0b24tZW5oYW5jZWQ6OmJlZm9yZSB7XFxuICAgIGNvbnRlbnQ6ICcnO1xcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XFxuICAgIHRvcDogMDtcXG4gICAgbGVmdDogLTEwMCU7XFxuICAgIHdpZHRoOiAxMDAlO1xcbiAgICBoZWlnaHQ6IDEwMCU7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSA1MCUsIHRyYW5zcGFyZW50IDEwMCUpO1xcbiAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNnMgZWFzZTtcXG4gIH1cXG5cXG4gIC5nbGFzcy1idXR0b24tZW5oYW5jZWQ6aG92ZXI6OmJlZm9yZSB7XFxuICAgIGxlZnQ6IDEwMCU7XFxuICB9XFxuXFxuICAuZ2xhc3MtYnV0dG9uLWVuaGFuY2VkOmhvdmVyIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpIHNjYWxlKDEuMDMpO1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMjUpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpIDEwMCUpO1xcbiAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KTtcXG4gICAgYm94LXNoYWRvdzogMCAyMHB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjIpLCAwIDAgMCAxcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIGluc2V0O1xcbiAgfVxcblxcbiAgLyogUHJlbWl1bSBnbGFzcyBidXR0b24gZm9yIGhlcm8gYW5kIG1haW4gQ1RBcyAqL1xcbiAgLmdsYXNzLWJ1dHRvbiB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xMikgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNikgMTAwJSk7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDI1cHgpO1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjVweCk7XFxuICAgIGJvcmRlcjogMS41cHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xcbiAgICBwYWRkaW5nOiAxcmVtIDJyZW07XFxuICAgIGZvbnQtc2l6ZTogMXJlbTtcXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG5cXG4gIC5nbGFzcy1idXR0b246OmJlZm9yZSB7XFxuICAgIGNvbnRlbnQ6ICcnO1xcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XFxuICAgIHRvcDogMDtcXG4gICAgbGVmdDogLTEwMCU7XFxuICAgIHdpZHRoOiAxMDAlO1xcbiAgICBoZWlnaHQ6IDEwMCU7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgNTAlLCB0cmFuc3BhcmVudCAxMDAlKTtcXG4gICAgdHJhbnNpdGlvbjogbGVmdCAwLjVzIGVhc2U7XFxuICB9XFxuXFxuICAuZ2xhc3MtYnV0dG9uOmhvdmVyOjpiZWZvcmUge1xcbiAgICBsZWZ0OiAxMDAlO1xcbiAgfVxcblxcbiAgLmdsYXNzLWJ1dHRvbjpob3ZlciB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KSBzY2FsZSgxLjAyKTtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE4KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEwKSAxMDAlKTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XFxuICAgIGJveC1zaGFkb3c6IDAgMTVweCA0NXB4IHJnYmEoMCwgMCwgMCwgMC4xNSksIDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgaW5zZXQ7XFxuICB9XFxuXFxuICAvKiBHbGFzcyBjYXJkIGZvciBoZXJvIHN0YXRzIGFuZCBmZWF0dXJlIGNhcmRzICovXFxuICAuZ2xhc3MtY2FyZCB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNCkgMTAwJSk7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7XFxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgfVxcblxcbiAgLmdsYXNzLWNhcmQ6aG92ZXIge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTIpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDYpIDEwMCUpO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCkgc2NhbGUoMS4wMik7XFxuICAgIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KTtcXG4gIH1cXG4gIFxcbiAgLyogRW5oYW5jZWQgQ2FyZCBzdHlsZXMgd2l0aCBnbGFzcyBlZmZlY3RzICovXFxuICAuY2FyZCB7XFxuICAgIEBhcHBseSBiZy13aGl0ZS84MCBkYXJrOmJnLWdyYXktODAwLzgwIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIGRhcms6Ym9yZGVyLWdyYXktNzAwLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMDtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gIH1cXG5cXG4gIC5jYXJkLWhvdmVyIHtcXG4gICAgQGFwcGx5IGhvdmVyOnNoYWRvdy14bCBob3ZlcjotdHJhbnNsYXRlLXktMiB0cmFuc2Zvcm0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNTtcXG4gIH1cXG5cXG4gIC5jYXJkLWdsYXNzIHtcXG4gICAgQGFwcGx5IHJvdW5kZWQtMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMDtcXG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjRweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyNHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gICAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICB9XFxuXFxuICAuY2FyZC1nbGFzczpob3ZlciB7XFxuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xMik7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNHB4KSBzY2FsZSgxLjAyKTtcXG4gICAgYm94LXNoYWRvdzogMCAyMHB4IDYwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcXG4gIH1cXG5cXG4gIC5jYXJkLXByZW1pdW0ge1xcbiAgICBAYXBwbHkgcm91bmRlZC0zeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwO1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgMTAwJSk7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDMwcHgpO1xcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMzBweCk7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gICAgYm94LXNoYWRvdzogMCAyNXB4IDUwcHggcmdiYSgwLCAwLCAwLCAwLjEpLCBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIH1cXG5cXG4gIC5jYXJkLXByZW1pdW06aG92ZXIge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLThweCkgc2NhbGUoMS4wMyk7XFxuICAgIGJveC1zaGFkb3c6IDAgMzVweCA3MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgfVxcbiAgXFxuICAvKiBJbnB1dCBzdHlsZXMgKi9cXG4gIC5pbnB1dC1maWVsZCB7XFxuICAgIEBhcHBseSB3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDA7XFxuICB9XFxuICBcXG4gIC8qIFNlY3Rpb24gc3BhY2luZyAqL1xcbiAgLnNlY3Rpb24tcGFkZGluZyB7XFxuICAgIEBhcHBseSBweS0xNiBsZzpweS0yNDtcXG4gIH1cXG4gIFxcbiAgLmNvbnRhaW5lci1wYWRkaW5nIHtcXG4gICAgQGFwcGx5IHB4LTQgc206cHgtNiBsZzpweC04O1xcbiAgfVxcbiAgXFxuICAvKiBFbmhhbmNlZCBUeXBvZ3JhcGh5IHdpdGggcHJlbWl1bSBzdHlsaW5nIC0gRGFyayB0aGVtZSBvcHRpbWl6ZWQgKi9cXG4gIC5oZWFkaW5nLWhlcm8ge1xcbiAgICBmb250LXNpemU6IGNsYW1wKDMuNXJlbSwgMTJ2dywgOHJlbSk7XFxuICAgIGZvbnQtd2VpZ2h0OiA5MDA7XFxuICAgIGxpbmUtaGVpZ2h0OiAwLjg1O1xcbiAgICBsZXR0ZXItc3BhY2luZzogLTAuMDRlbTtcXG4gICAgQGFwcGx5IGZvbnQtY2Fpcm87XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmMWY1ZjkgNTAlLCAjZTJlOGYwIDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICAgIHRleHQtc2hhZG93OiAwIDhweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC40KTtcXG4gICAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjIpKTtcXG4gIH1cXG5cXG4gIC5oZWFkaW5nLWhlcm8tbW9iaWxlIHtcXG4gICAgZm9udC1zaXplOiBjbGFtcCgyLjVyZW0sIDE1dncsIDRyZW0pO1xcbiAgICBmb250LXdlaWdodDogODAwO1xcbiAgICBsaW5lLWhlaWdodDogMC45O1xcbiAgICBsZXR0ZXItc3BhY2luZzogLTAuMDNlbTtcXG4gICAgQGFwcGx5IGZvbnQtY2Fpcm87XFxuICB9XFxuXFxuICAuaGVhZGluZy14bCB7XFxuICAgIEBhcHBseSB0ZXh0LTR4bCBsZzp0ZXh0LTV4bCB4bDp0ZXh0LTZ4bCBmb250LWJvbGQgdHJhY2tpbmctdGlnaHQgZm9udC1kaXNwbGF5O1xcbiAgfVxcblxcbiAgLmhlYWRpbmctbGcge1xcbiAgICBAYXBwbHkgdGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0IGZvbnQtZGlzcGxheTtcXG4gIH1cXG5cXG4gIC5oZWFkaW5nLW1kIHtcXG4gICAgQGFwcGx5IHRleHQtMnhsIGxnOnRleHQtM3hsIGZvbnQtc2VtaWJvbGQgdHJhY2tpbmctdGlnaHQ7XFxuICB9XFxuXFxuICAuaGVhZGluZy1zbSB7XFxuICAgIEBhcHBseSB0ZXh0LXhsIGxnOnRleHQtMnhsIGZvbnQtc2VtaWJvbGQ7XFxuICB9XFxuXFxuICAvKiBFbmhhbmNlZCBncmFkaWVudCB0ZXh0IGVmZmVjdHMgKi9cXG4gIC5ncmFkaWVudC10ZXh0IHtcXG4gICAgQGFwcGx5IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5LTYwMCB2aWEtc2Vjb25kYXJ5LTUwMCB0by1wcmltYXJ5LTcwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudDtcXG4gICAgYmFja2dyb3VuZC1zaXplOiAyMDAlIDIwMCU7XFxuICAgIGFuaW1hdGlvbjogZ3JhZGllbnRTaGlmdCAzcyBlYXNlLWluLW91dCBpbmZpbml0ZTtcXG4gIH1cXG5cXG4gIC5ncmFkaWVudC10ZXh0LXByZW1pdW0ge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZmZmZmIDAlLCAjZjFmNWY5IDMwJSwgI2UyZThmMCA2MCUsICNjYmQ1ZTEgMTAwJSk7XFxuICAgIGJhY2tncm91bmQtc2l6ZTogMzAwJSAzMDAlO1xcbiAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICAgIGFuaW1hdGlvbjogcHJlbWl1bUdyYWRpZW50IDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgICB0ZXh0LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcXG4gIH1cXG5cXG4gIC8qIFByZW1pdW0gR29sZC9NZXRhbGxpYyBUZXh0IEVmZmVjdHMgKi9cXG4gIC5ncmFkaWVudC10ZXh0LWdvbGQge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZkNzAwIDAlLCAjZmZlZDRlIDI1JSwgI2ZmZDcwMCA1MCUsICNmZmIzMDAgNzUlLCAjZmZkNzAwIDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDQwMCUgNDAwJTtcXG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgICBhbmltYXRpb246IGdvbGRTaGluZSAzcyBlYXNlLWluLW91dCBpbmZpbml0ZTtcXG4gICAgdGV4dC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgyNTUsIDIxNSwgMCwgMC40KTtcXG4gICAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDJweCA4cHggcmdiYSgyNTUsIDIxNSwgMCwgMC4zKSk7XFxuICB9XFxuXFxuICAuZ3JhZGllbnQtdGV4dC1nb2xkLXByZW1pdW0ge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsXFxuICAgICAgIzhCNjkxNCAwJSxcXG4gICAgICAjQjg4NjBCIDEwJSxcXG4gICAgICAjREFBNTIwIDIwJSxcXG4gICAgICAjRkZENzAwIDMwJSxcXG4gICAgICAjRkZFRDRFIDQwJSxcXG4gICAgICAjRkZGOERDIDUwJSxcXG4gICAgICAjRkZFRDRFIDYwJSxcXG4gICAgICAjRkZENzAwIDcwJSxcXG4gICAgICAjREFBNTIwIDgwJSxcXG4gICAgICAjQjg4NjBCIDkwJSxcXG4gICAgICAjOEI2OTE0IDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDMwMCUgMTAwJTtcXG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgICBhbmltYXRpb246IHByZW1pdW1Hb2xkU2hpbmUgNHMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XFxuICAgIHRleHQtc2hhZG93OiAwIDhweCAyMHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuNik7XFxuICAgIGZpbHRlcjogZHJvcC1zaGFkb3coMCA2cHggMTZweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjUpKSBkcm9wLXNoYWRvdygwIDAgMzBweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpKSBkcm9wLXNoYWRvdygwIDJweCA4cHggcmdiYSgxODQsIDEzNCwgMTEsIDAuNCkpO1xcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICB9XFxuXFxuICAvKiBTaGFycCB2ZXJzaW9uIHdpdGhvdXQgYmx1ciBlZmZlY3RzIGZvciBoZXJvIHRpdGxlICovXFxuICAuZ3JhZGllbnQtdGV4dC1nb2xkLXByZW1pdW0tc2hhcnAge1xcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsXFxuICAgICAgIzhCNjkxNCAwJSxcXG4gICAgICAjQjg4NjBCIDEwJSxcXG4gICAgICAjREFBNTIwIDIwJSxcXG4gICAgICAjRkZENzAwIDMwJSxcXG4gICAgICAjRkZFRDRFIDQwJSxcXG4gICAgICAjRkZGOERDIDUwJSxcXG4gICAgICAjRkZFRDRFIDYwJSxcXG4gICAgICAjRkZENzAwIDcwJSxcXG4gICAgICAjREFBNTIwIDgwJSxcXG4gICAgICAjQjg4NjBCIDkwJSxcXG4gICAgICAjOEI2OTE0IDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDMwMCUgMTAwJTtcXG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgICBhbmltYXRpb246IHByZW1pdW1Hb2xkU2hpbmVTaGFycCA0cyBlYXNlLWluLW91dCBpbmZpbml0ZTtcXG4gICAgLyogUmVtb3ZlZCBhbGwgYmx1ci1yZWxhdGVkIHByb3BlcnRpZXMgKi9cXG4gICAgdGV4dC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjQpO1xcbiAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpKTtcXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xcbiAgICAvKiBFbmhhbmNlZCB0ZXh0IHJlbmRlcmluZyBmb3Igc2hhcnBuZXNzICovXFxuICAgIHRleHQtcmVuZGVyaW5nOiBvcHRpbWl6ZUxlZ2liaWxpdHk7XFxuICAgIC13ZWJraXQtZm9udC1zbW9vdGhpbmc6IGFudGlhbGlhc2VkO1xcbiAgICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlO1xcbiAgICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFxcXCJrZXJuXFxcIiAxLCBcXFwibGlnYVxcXCIgMTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApO1xcbiAgICB3aWxsLWNoYW5nZTogYmFja2dyb3VuZC1wb3NpdGlvbjtcXG4gICAgYmFja2ZhY2UtdmlzaWJpbGl0eTogaGlkZGVuO1xcbiAgfVxcblxcblxcblxcbiAgLnRleHQtZ2xhc3Mge1xcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xcbiAgICB0ZXh0LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG5cXG4gIC8qIEVuaGFuY2VkIEFyYWJpYyB0eXBvZ3JhcGh5ICovXFxuICAudGV4dC1hcmFiaWMge1xcbiAgICBAYXBwbHkgZm9udC1jYWlybztcXG4gICAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBcXFwia2VyblxcXCIgMSwgXFxcImxpZ2FcXFwiIDEsIFxcXCJjYWx0XFxcIiAxLCBcXFwic3MwMVxcXCIgMSwgXFxcInNzMDJcXFwiIDE7XFxuICAgIGZvbnQtdmFyaWFudC1saWdhdHVyZXM6IGNvbW1vbi1saWdhdHVyZXMgY29udGV4dHVhbDtcXG4gICAgdGV4dC1yZW5kZXJpbmc6IG9wdGltaXplTGVnaWJpbGl0eTtcXG4gIH1cXG5cXG4gIC50ZXh0LWFyYWJpYy1kaXNwbGF5IHtcXG4gICAgQGFwcGx5IGZvbnQtY2Fpcm87XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMTtcXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcXG4gIH1cXG5cXG4gIC50ZXh0LWFyYWJpYy1wcmVtaXVtIHtcXG4gICAgQGFwcGx5IGZvbnQtY2Fpcm87XFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcImtlcm5cXFwiIDEsIFxcXCJsaWdhXFxcIiAxLCBcXFwiY2FsdFxcXCIgMSwgXFxcInNzMDFcXFwiIDEsIFxcXCJzczAyXFxcIiAxO1xcbiAgICBmb250LXZhcmlhbnQtbGlnYXR1cmVzOiBjb21tb24tbGlnYXR1cmVzIGNvbnRleHR1YWw7XFxuICAgIHRleHQtcmVuZGVyaW5nOiBvcHRpbWl6ZUxlZ2liaWxpdHk7XFxuICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xcbiAgICB0ZXh0LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7XFxuICAgIGxldHRlci1zcGFjaW5nOiAwLjAyNWVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4zO1xcbiAgICBmb250LXdlaWdodDogNzAwO1xcbiAgfVxcblxcbiAgLyogUHJlbWl1bSBzZWN0aW9uIGhlYWRlcnMgd2l0aCBTeXJpYW4gY3VsdHVyYWwgZWxlbWVudHMgKi9cXG4gIC5zZWN0aW9uLWhlYWRlci1wcmVtaXVtIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZmZmZiAwJSwgI2YxZjVmOSAzMCUsICNlMmU4ZjAgNjAlLCAjY2JkNWUxIDEwMCUpO1xcbiAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbiAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICAgIHRleHQtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjMpO1xcbiAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMikpO1xcbiAgfVxcblxcbiAgLyogR29sZCBhY2NlbnQgZWxlbWVudHMgKi9cXG4gIC5nb2xkLWFjY2VudC12ZXJ0aWNhbCB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICNGRkQ3MDAgMCUsICNCODg2MEIgMTAwJSk7XFxuICAgIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7XFxuICB9XFxuXFxuICAvKiBQcmVtaXVtIGdsYXNzIG1vcnBoaXNtIGNhcmRzICovXFxuICAuZ2xhc3MtY2FyZC1wcmVtaXVtIHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA0KSAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjVweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyNXB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gICAgYm9yZGVyLXJhZGl1czogMjBweDtcXG4gICAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgfVxcblxcbiAgLmdsYXNzLWNhcmQtcHJlbWl1bTpob3ZlciB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtOHB4KSBzY2FsZSgxLjAyKTtcXG4gICAgYm94LXNoYWRvdzogMCAyMHB4IDYwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEyKSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA2KSAxMDAlKTtcXG4gIH1cXG5cXG4gIC8qIEdvbGQgZ3JhZGllbnQgYnV0dG9ucyAqL1xcbiAgLmJ0bi1nb2xkLWdyYWRpZW50IHtcXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0ZGRDcwMCAwJSwgI0I4ODYwQiAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7XFxuICAgIGNvbG9yOiB3aGl0ZTtcXG4gICAgdGV4dC1zaGFkb3c6IDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMyk7XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICAgIG92ZXJmbG93OiBoaWRkZW47XFxuICB9XFxuXFxuICAuYnRuLWdvbGQtZ3JhZGllbnQ6aG92ZXIge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCkgc2NhbGUoMS4wMik7XFxuICAgIGJveC1zaGFkb3c6IDAgMTJweCAzNXB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuNCk7XFxuICB9XFxuXFxuICAvKiBTaGltbWVyIGVmZmVjdCBmb3IgYnV0dG9ucyBhbmQgY2FyZHMgKi9cXG4gIC5zaGltbWVyLWVmZmVjdCB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSA1MCUsIHRyYW5zcGFyZW50IDEwMCUpO1xcbiAgICBhbmltYXRpb246IGdsYXNzU2hpbW1lciAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgfVxcblxcbiAgLyogUG9wdWxhciBiYWRnZSBzdHlsaW5nICovXFxuICAucG9wdWxhci1iYWRnZSB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGRkQ3MDAgMCUsICNCODg2MEIgMTAwJSk7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpO1xcbiAgICBib3JkZXItcmFkaXVzOiAyNXB4O1xcbiAgfVxcblxcbiAgLyogVGFiIG5hdmlnYXRpb24gcHJlbWl1bSBzdHlsaW5nICovXFxuICAudGFiLW5hdmlnYXRpb24tcHJlbWl1bSB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSAxMDAlKTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjVweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyNXB4KTtcXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gICAgYm9yZGVyLXJhZGl1czogMTZweDtcXG4gIH1cXG5cXG4gIC8qIFByZW1pdW0gR29sZC9NZXRhbGxpYyBCdXR0b25zICovXFxuICAuYnRuLWdvbGQtcHJlbWl1bSB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsXFxuICAgICAgIzhCNjkxNCAwJSxcXG4gICAgICAjQjg4NjBCIDE1JSxcXG4gICAgICAjREFBNTIwIDMwJSxcXG4gICAgICAjRkZENzAwIDQ1JSxcXG4gICAgICAjRkZFRDRFIDYwJSxcXG4gICAgICAjRkZENzAwIDc1JSxcXG4gICAgICAjREFBNTIwIDkwJSxcXG4gICAgICAjQjg4NjBCIDEwMCUpO1xcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDMwMCUgMzAwJTtcXG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG4gICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDIxNSwgMCwgMC40KTtcXG4gICAgYm94LXNoYWRvdzpcXG4gICAgICAwIDhweCAyNXB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuNCksXFxuICAgICAgMCA0cHggMTVweCByZ2JhKDE4NCwgMTM0LCAxMSwgMC4zKSxcXG4gICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSxcXG4gICAgICBpbnNldCAwIC0xcHggMCByZ2JhKDEzOSwgMTA1LCAyMCwgMC4yKTtcXG4gICAgY29sb3I6ICMxYTFhMWE7XFxuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XFxuICAgIHRleHQtc2hhZG93OiAwIDFweCAycHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpO1xcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xcbiAgICBwYWRkaW5nOiAxcmVtIDJyZW07XFxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UtaW4tb3V0O1xcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICAgIG92ZXJmbG93OiBoaWRkZW47XFxuICAgIGFuaW1hdGlvbjogZ29sZFNoaW5lIDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgfVxcblxcbiAgLmJ0bi1nb2xkLXByZW1pdW06OmJlZm9yZSB7XFxuICAgIGNvbnRlbnQ6ICcnO1xcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XFxuICAgIHRvcDogMDtcXG4gICAgbGVmdDogLTEwMCU7XFxuICAgIHdpZHRoOiAxMDAlO1xcbiAgICBoZWlnaHQ6IDEwMCU7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KSA1MCUsIHRyYW5zcGFyZW50IDEwMCUpO1xcbiAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNnMgZWFzZTtcXG4gIH1cXG5cXG4gIC5idG4tZ29sZC1wcmVtaXVtOmhvdmVyOjpiZWZvcmUge1xcbiAgICBsZWZ0OiAxMDAlO1xcbiAgfVxcblxcbiAgLmJ0bi1nb2xkLXByZW1pdW06aG92ZXIge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTNweCkgc2NhbGUoMS4wNSk7XFxuICAgIGJveC1zaGFkb3c6XFxuICAgICAgMCAxNXB4IDQwcHggcmdiYSgyNTUsIDIxNSwgMCwgMC41KSxcXG4gICAgICAwIDhweCAyNXB4IHJnYmEoMTg0LCAxMzQsIDExLCAwLjQpLFxcbiAgICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpLFxcbiAgICAgIGluc2V0IDAgLTFweCAwIHJnYmEoMTM5LCAxMDUsIDIwLCAwLjMpO1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAxMDAlIDEwMCU7XFxuICAgIGZpbHRlcjogYnJpZ2h0bmVzcygxLjEpIHNhdHVyYXRlKDEuMSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDIxNSwgMCwgMC42KTtcXG4gIH1cXG5cXG4gIC8qIE1ldGFsbGljIENhcmQgRWZmZWN0cyAqL1xcbiAgLmNhcmQtbWV0YWxsaWMtZ29sZCB7XFxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyMTUsIDAsIDAuMSkgMCUsIHJnYmEoMjU1LCAyMTUsIDAsIDAuMDUpIDEwMCUpO1xcbiAgICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyNXB4KTtcXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDI1cHgpO1xcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjE1LCAwLCAwLjIpO1xcbiAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMSksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlLWluLW91dDtcXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xcbiAgfVxcblxcbiAgLmNhcmQtbWV0YWxsaWMtZ29sZDo6YmVmb3JlIHtcXG4gICAgY29udGVudDogJyc7XFxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcXG4gICAgdG9wOiAtNTAlO1xcbiAgICBsZWZ0OiAtNTAlO1xcbiAgICB3aWR0aDogMjAwJTtcXG4gICAgaGVpZ2h0OiAyMDAlO1xcbiAgICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDI1NSwgMjE1LCAwLCAwLjEpIDAlLCB0cmFuc3BhcmVudCA3MCUpO1xcbiAgICBhbmltYXRpb246IGdvbGRHbG93IDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcXG4gIH1cXG5cXG4gIC5jYXJkLW1ldGFsbGljLWdvbGQ6aG92ZXIge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLThweCkgc2NhbGUoMS4wMik7XFxuICAgIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMiksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyMTUsIDAsIDAuNCk7XFxuICB9XFxuICBcXG4gIC8qIEFuaW1hdGlvbiB1dGlsaXRpZXMgKi9cXG4gIC5hbmltYXRlLWZhZGUtaW4tdXAge1xcbiAgICBhbmltYXRpb246IGZhZGVJblVwIDAuNnMgZWFzZS1vdXQgZm9yd2FyZHM7XFxuICB9XFxuICBcXG4gIC5hbmltYXRlLWZhZGUtaW4tZG93biB7XFxuICAgIGFuaW1hdGlvbjogZmFkZUluRG93biAwLjZzIGVhc2Utb3V0IGZvcndhcmRzO1xcbiAgfVxcbiAgXFxuICAuYW5pbWF0ZS1mYWRlLWluLWxlZnQge1xcbiAgICBhbmltYXRpb246IGZhZGVJbkxlZnQgMC42cyBlYXNlLW91dCBmb3J3YXJkcztcXG4gIH1cXG4gIFxcbiAgLmFuaW1hdGUtZmFkZS1pbi1yaWdodCB7XFxuICAgIGFuaW1hdGlvbjogZmFkZUluUmlnaHQgMC42cyBlYXNlLW91dCBmb3J3YXJkcztcXG4gIH1cXG59XFxuXFxuLyogVXRpbGl0eSBjbGFzc2VzICovXFxuQGxheWVyIHV0aWxpdGllcyB7XFxuICAvKiBUZXh0IGRpcmVjdGlvbiB1dGlsaXRpZXMgKi9cXG4gIC50ZXh0LXN0YXJ0IHtcXG4gICAgdGV4dC1hbGlnbjogc3RhcnQ7XFxuICB9XFxuICBcXG4gIC50ZXh0LWVuZCB7XFxuICAgIHRleHQtYWxpZ246IGVuZDtcXG4gIH1cXG4gIFxcbiAgLyogTWFyZ2luIHV0aWxpdGllcyBmb3IgUlRMICovXFxuICAubXMtYXV0byB7XFxuICAgIG1hcmdpbi1pbmxpbmUtc3RhcnQ6IGF1dG87XFxuICB9XFxuICBcXG4gIC5tZS1hdXRvIHtcXG4gICAgbWFyZ2luLWlubGluZS1lbmQ6IGF1dG87XFxuICB9XFxuICBcXG4gIC8qIFBhZGRpbmcgdXRpbGl0aWVzIGZvciBSVEwgKi9cXG4gIC5wcy00IHtcXG4gICAgcGFkZGluZy1pbmxpbmUtc3RhcnQ6IDFyZW07XFxuICB9XFxuICBcXG4gIC5wZS00IHtcXG4gICAgcGFkZGluZy1pbmxpbmUtZW5kOiAxcmVtO1xcbiAgfVxcbn1cXG5cXG4vKiBLZXlmcmFtZSBhbmltYXRpb25zICovXFxuQGtleWZyYW1lcyBmYWRlSW5VcCB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDMwcHgpO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgZmFkZUluRG93biB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zMHB4KTtcXG4gIH1cXG4gIHRvIHtcXG4gICAgb3BhY2l0eTogMTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGZhZGVJbkxlZnQge1xcbiAgZnJvbSB7XFxuICAgIG9wYWNpdHk6IDA7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMzBweCk7XFxuICB9XFxuICB0byB7XFxuICAgIG9wYWNpdHk6IDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBmYWRlSW5SaWdodCB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDMwcHgpO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XFxuICB9XFxufVxcblxcbi8qIEVuaGFuY2VkIGtleWZyYW1lIGFuaW1hdGlvbnMgZm9yIGdsYXNzIGVmZmVjdHMgKi9cXG5Aa2V5ZnJhbWVzIGdyYWRpZW50U2hpZnQge1xcbiAgMCUsIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSA1MCU7IH1cXG4gIDUwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgNTAlOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgcHJlbWl1bUdyYWRpZW50IHtcXG4gIDAlLCAxMDAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgMCU7IH1cXG4gIDI1JSB7IGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgMCU7IH1cXG4gIDUwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgMTAwJTsgfVxcbiAgNzUlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgMTAwJTsgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGdsYXNzU2hpbW1lciB7XFxuICAwJSB7IGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDA7IH1cXG4gIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAyMDAlIDA7IH1cXG59XFxuXFxuQGtleWZyYW1lcyBmbG9hdEdsYXNzIHtcXG4gIDAlLCAxMDAlIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDBweCkgcm90YXRlKDBkZWcpO1xcbiAgICAvKiBSZW1vdmVkIGJsdXIgZmlsdGVyICovXFxuICB9XFxuICAyNSUge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpIHJvdGF0ZSgxZGVnKTtcXG4gICAgLyogUmVtb3ZlZCBibHVyIGZpbHRlciAqL1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yMHB4KSByb3RhdGUoMGRlZyk7XFxuICAgIC8qIFJlbW92ZWQgYmx1ciBmaWx0ZXIgKi9cXG4gIH1cXG4gIDc1JSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCkgcm90YXRlKC0xZGVnKTtcXG4gICAgLyogUmVtb3ZlZCBibHVyIGZpbHRlciAqL1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHB1bHNlR2xhc3Mge1xcbiAgMCUsIDEwMCUge1xcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbiAgICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIH1cXG4gIDUwJSB7XFxuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xcbiAgICBib3gtc2hhZG93OiAwIDEycHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xcbiAgfVxcbn1cXG5cXG4vKiBQcmVtaXVtIEdvbGQvTWV0YWxsaWMgQW5pbWF0aW9ucyAqL1xcbkBrZXlmcmFtZXMgZ29sZFNoaW5lIHtcXG4gIDAlLCAxMDAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlOyB9XFxuICA1MCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAxMDAlIDUwJTsgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHByZW1pdW1Hb2xkU2hpbmUge1xcbiAgMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAtMjAwJSAwJTsgfVxcbiAgNTAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgMCU7IH1cXG4gIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAyMDAlIDAlOyB9XFxufVxcblxcbi8qIFNoYXJwIGFuaW1hdGlvbiB3aXRob3V0IGFueSBibHVyIGVmZmVjdHMgKi9cXG5Aa2V5ZnJhbWVzIHByZW1pdW1Hb2xkU2hpbmVTaGFycCB7XFxuICAwJSB7XFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDAlO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVooMCk7XFxuICB9XFxuICA1MCUge1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAwJTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApO1xcbiAgfVxcbiAgMTAwJSB7XFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDIwMCUgMCU7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWigwKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBnb2xkR2xpbW1lciB7XFxuICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMTAwJSkgc2tld1goLTE1ZGVnKTsgfVxcbiAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgyMDAlKSBza2V3WCgtMTVkZWcpOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgdGV4dFNoaW1tZXIge1xcbiAgMCUge1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAtMjAwJSAwO1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgMDtcXG4gICAgb3BhY2l0eTogMTtcXG4gIH1cXG4gIDEwMCUge1xcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAyMDAlIDA7XFxuICAgIG9wYWNpdHk6IDA7XFxuICB9XFxufVxcblxcblxcblxcbkBrZXlmcmFtZXMgZ29sZEdsb3cge1xcbiAgMCUsIDEwMCUge1xcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKSBzY2FsZSgxKTtcXG4gICAgb3BhY2l0eTogMC4zO1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKSBzY2FsZSgxLjEpO1xcbiAgICBvcGFjaXR5OiAwLjY7XFxuICB9XFxufVxcblxcbi8qIFJlZHVjZWQgbW90aW9uIHByZWZlcmVuY2VzICovXFxuQG1lZGlhIChwcmVmZXJzLXJlZHVjZWQtbW90aW9uOiByZWR1Y2UpIHtcXG4gICosXFxuICAqOjpiZWZvcmUsXFxuICAqOjphZnRlciB7XFxuICAgIGFuaW1hdGlvbi1kdXJhdGlvbjogMC4wMW1zICFpbXBvcnRhbnQ7XFxuICAgIGFuaW1hdGlvbi1pdGVyYXRpb24tY291bnQ6IDEgIWltcG9ydGFudDtcXG4gICAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMC4wMW1zICFpbXBvcnRhbnQ7XFxuICAgIHNjcm9sbC1iZWhhdmlvcjogYXV0byAhaW1wb3J0YW50O1xcbiAgfVxcbn1cXG5cXG4vKiBQcmludCBzdHlsZXMgKi9cXG5AbWVkaWEgcHJpbnQge1xcbiAgLm5vLXByaW50IHtcXG4gICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xcbiAgfVxcblxcbiAgLmdsYXNzLFxcbiAgLmdsYXNzLWNhcmQsXFxuICAuZ2xhc3MtYnV0dG9uIHtcXG4gICAgYmFja2dyb3VuZDogd2hpdGUgIWltcG9ydGFudDtcXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBub25lICFpbXBvcnRhbnQ7XFxuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBub25lICFpbXBvcnRhbnQ7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNjY2MgIWltcG9ydGFudDtcXG4gIH1cXG59XFxuXCJdLFwic291cmNlUm9vdFwiOlwiXCJ9XSk7XG4vLyBFeHBvcnRzXG5leHBvcnQgZGVmYXVsdCBfX19DU1NfTE9BREVSX0VYUE9SVF9fXztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ })

});