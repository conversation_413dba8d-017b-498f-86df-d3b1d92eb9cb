import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { useTheme as useNextTheme } from 'next-themes';
import { useTheme } from '@/themes';
import {
  Bars3Icon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
  LanguageIcon,
  BriefcaseIcon,
  SwatchIcon
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { theme: nextTheme, setTheme: setNextTheme } = useNextTheme();
  const { currentTheme, themeName, switchTheme, isGoldTheme, isPurpleTheme } = useTheme();
  const { locale } = router;
  const isRTL = locale === 'ar';

  // Fix hydration issue with next-themes
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Navigation items
  const navItems = [
    { key: 'features', href: '#features' },
    { key: 'howItWorks', href: '#how-it-works' },
    { key: 'pricing', href: '#pricing' },
    { key: 'about', href: '#about' },
    { key: 'contact', href: '#contact' },
  ];

  // Language toggle
  const toggleLanguage = () => {
    const newLocale = locale === 'ar' ? 'en' : 'ar';
    router.push(router.pathname, router.asPath, { locale: newLocale });
  };

  // Theme toggle (Next.js theme)
  const toggleNextTheme = () => {
    setNextTheme(nextTheme === 'dark' ? 'light' : 'dark');
  };

  // Custom theme toggle (Gold/Purple)
  const toggleCustomTheme = () => {
    const newTheme = themeName === 'gold' ? 'purple' : 'gold';
    switchTheme(newTheme);
  };

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
      style={{
        background: isScrolled
          ? `${currentTheme.colors.glass.background}, ${currentTheme.backgrounds.secondary}`
          : 'transparent',
        backdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : 'none',
        WebkitBackdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : 'none',
        borderBottom: isScrolled ? `1px solid ${currentTheme.colors.glass.border}` : 'none',
        boxShadow: isScrolled ? currentTheme.colors.glass.shadow : 'none',
      }}
    >
      <nav className="container mx-auto container-padding">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Enhanced Logo with Theme Integration */}
          <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse group">
            <motion.div
              className="relative w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center overflow-hidden"
              style={{
                background: currentTheme.gradients.primary,
                boxShadow: currentTheme.shadows.md,
              }}
              whileHover={{ scale: 1.05, rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <BriefcaseIcon className="w-5 h-5 lg:w-6 lg:h-6 text-white relative z-10" />
              {/* Shimmer effect */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                  animation: 'glassShimmer 1.5s ease-in-out infinite'
                }}
              />
            </motion.div>
            <motion.span
              className={`text-xl lg:text-2xl font-bold ${
                isRTL ? 'font-cairo' : 'font-display'
              }`}
              style={{
                background: currentTheme.gradients.text,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              {isRTL ? 'فريلا سوريا' : 'Freela Syria'}
            </motion.span>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {navItems.map((item) => (
              <motion.a
                key={item.key}
                href={item.href}
                className={`relative font-medium transition-all duration-300 ${
                  isRTL ? 'font-tajawal' : 'font-sans'
                }`}
                style={{
                  color: currentTheme.colors.text.secondary,
                }}
                onClick={(e) => {
                  e.preventDefault();
                  document.querySelector(item.href)?.scrollIntoView({
                    behavior: 'smooth'
                  });
                }}
                whileHover={{
                  scale: 1.05,
                  color: currentTheme.colors.text.accent,
                }}
                transition={{ duration: 0.2 }}
              >
                {t(`navigation.${item.key}`)}
                {/* Hover underline effect */}
                <motion.div
                  className="absolute bottom-0 left-0 h-0.5 rounded-full"
                  style={{ background: currentTheme.gradients.primary }}
                  initial={{ width: 0 }}
                  whileHover={{ width: '100%' }}
                  transition={{ duration: 0.3 }}
                />
              </motion.a>
            ))}
          </div>

          {/* Enhanced Desktop Actions */}
          <div className="hidden lg:flex items-center space-x-4 rtl:space-x-reverse">
            {/* Custom Theme Toggle (Gold/Purple) */}
            <motion.button
              type="button"
              onClick={toggleCustomTheme}
              className="relative p-2 rounded-lg overflow-hidden group"
              style={{
                background: currentTheme.colors.glass.background,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                border: `1px solid ${currentTheme.colors.glass.border}`,
              }}
              aria-label="Toggle custom theme"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <SwatchIcon
                className="w-5 h-5 relative z-10"
                style={{ color: currentTheme.colors.text.accent }}
              />
              {/* Shimmer effect */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                  animation: 'glassShimmer 1.5s ease-in-out infinite'
                }}
              />
            </motion.button>

            {/* Next.js Theme Toggle */}
            <motion.button
              type="button"
              onClick={toggleNextTheme}
              className="relative p-2 rounded-lg overflow-hidden group"
              style={{
                background: currentTheme.colors.glass.background,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                border: `1px solid ${currentTheme.colors.glass.border}`,
              }}
              aria-label="Toggle dark/light theme"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {mounted ? (
                nextTheme === 'dark' ? (
                  <SunIcon
                    className="w-5 h-5 relative z-10"
                    style={{ color: currentTheme.colors.text.accent }}
                  />
                ) : (
                  <MoonIcon
                    className="w-5 h-5 relative z-10"
                    style={{ color: currentTheme.colors.text.accent }}
                  />
                )
              ) : (
                <div className="w-5 h-5" />
              )}
              {/* Shimmer effect */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                  animation: 'glassShimmer 1.5s ease-in-out infinite'
                }}
              />
            </motion.button>

            {/* Enhanced Language Toggle */}
            <motion.button
              type="button"
              onClick={toggleLanguage}
              className="relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse"
              style={{
                background: currentTheme.colors.glass.background,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                border: `1px solid ${currentTheme.colors.glass.border}`,
              }}
              aria-label="Toggle language"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <LanguageIcon
                className="w-5 h-5 relative z-10"
                style={{ color: currentTheme.colors.text.accent }}
              />
              <span
                className={`text-sm font-medium relative z-10 ${
                  isRTL ? 'font-cairo' : 'font-sans'
                }`}
                style={{ color: currentTheme.colors.text.primary }}
              >
                {locale === 'ar' ? 'EN' : 'عر'}
              </span>
              {/* Shimmer effect */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                  animation: 'glassShimmer 1.5s ease-in-out infinite'
                }}
              />
            </motion.button>

            {/* Enhanced CTA Buttons */}
            <motion.div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Link href="/login" passHref>
                <motion.a
                  className={`font-medium transition-all duration-300 ${
                    isRTL ? 'font-tajawal' : 'font-sans'
                  }`}
                  style={{ color: currentTheme.colors.text.accent }}
                  whileHover={{
                    scale: 1.05,
                    color: currentTheme.colors.text.primary
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {t('navigation.login')}
                </motion.a>
              </Link>

              <Link href="/signup" passHref>
                <motion.a
                  className={`relative px-6 py-2.5 rounded-xl font-semibold overflow-hidden group ${
                    isRTL ? 'font-cairo' : 'font-display'
                  }`}
                  style={{
                    background: currentTheme.gradients.button,
                    color: 'white',
                    boxShadow: currentTheme.shadows.md,
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <span className="relative z-10">
                    {t('navigation.joinAsExpert')}
                  </span>
                  {/* Button shimmer effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                      animation: 'glassShimmer 1.5s ease-in-out infinite'
                    }}
                  />
                </motion.a>
              </Link>
            </motion.div>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <motion.button
            type="button"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden relative p-2 rounded-lg overflow-hidden group"
            style={{
              background: currentTheme.colors.glass.background,
              backdropFilter: currentTheme.colors.glass.backdropBlur,
              WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
              border: `1px solid ${currentTheme.colors.glass.border}`,
            }}
            aria-label="Toggle menu"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={{ rotate: isMenuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isMenuOpen ? (
                <XMarkIcon
                  className="w-6 h-6 relative z-10"
                  style={{ color: currentTheme.colors.text.accent }}
                />
              ) : (
                <Bars3Icon
                  className="w-6 h-6 relative z-10"
                  style={{ color: currentTheme.colors.text.accent }}
                />
              )}
            </motion.div>
            {/* Shimmer effect */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                animation: 'glassShimmer 1.5s ease-in-out infinite'
              }}
            />
          </motion.button>
        </div>

        {/* Enhanced Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: 'auto', y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="lg:hidden overflow-hidden"
              style={{
                background: `${currentTheme.colors.glass.background}, ${currentTheme.backgrounds.secondary}`,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                borderTop: `1px solid ${currentTheme.colors.glass.border}`,
                boxShadow: currentTheme.colors.glass.shadow,
              }}
            >
              <div className="py-6 space-y-6">
                {navItems.map((item, index) => (
                  <motion.a
                    key={item.key}
                    href={item.href}
                    className={`block font-medium transition-all duration-300 ${
                      isRTL ? 'font-tajawal text-right' : 'font-sans text-left'
                    }`}
                    style={{
                      color: currentTheme.colors.text.secondary,
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      setIsMenuOpen(false);
                      document.querySelector(item.href)?.scrollIntoView({
                        behavior: 'smooth'
                      });
                    }}
                    initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    whileHover={{
                      scale: 1.02,
                      color: currentTheme.colors.text.accent,
                      x: isRTL ? -5 : 5,
                    }}
                  >
                    {t(`navigation.${item.key}`)}
                  </motion.a>
                ))}

                <motion.div
                  className="flex items-center justify-between pt-6 mt-6"
                  style={{
                    borderTop: `1px solid ${currentTheme.colors.glass.border}`,
                  }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.3 }}
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    {/* Custom Theme Toggle */}
                    <motion.button
                      type="button"
                      onClick={toggleCustomTheme}
                      className="relative p-2 rounded-lg overflow-hidden group"
                      style={{
                        background: currentTheme.colors.glass.background,
                        backdropFilter: currentTheme.colors.glass.backdropBlur,
                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                        border: `1px solid ${currentTheme.colors.glass.border}`,
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <SwatchIcon
                        className="w-5 h-5 relative z-10"
                        style={{ color: currentTheme.colors.text.accent }}
                      />
                    </motion.button>

                    {/* Next.js Theme Toggle */}
                    <motion.button
                      type="button"
                      onClick={toggleNextTheme}
                      className="relative p-2 rounded-lg overflow-hidden group"
                      style={{
                        background: currentTheme.colors.glass.background,
                        backdropFilter: currentTheme.colors.glass.backdropBlur,
                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                        border: `1px solid ${currentTheme.colors.glass.border}`,
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {mounted ? (
                        nextTheme === 'dark' ? (
                          <SunIcon
                            className="w-5 h-5 relative z-10"
                            style={{ color: currentTheme.colors.text.accent }}
                          />
                        ) : (
                          <MoonIcon
                            className="w-5 h-5 relative z-10"
                            style={{ color: currentTheme.colors.text.accent }}
                          />
                        )
                      ) : (
                        <div className="w-5 h-5" />
                      )}
                    </motion.button>

                    {/* Language Toggle */}
                    <motion.button
                      type="button"
                      onClick={toggleLanguage}
                      className="relative p-2 rounded-lg overflow-hidden group flex items-center space-x-1 rtl:space-x-reverse"
                      style={{
                        background: currentTheme.colors.glass.background,
                        backdropFilter: currentTheme.colors.glass.backdropBlur,
                        WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                        border: `1px solid ${currentTheme.colors.glass.border}`,
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <LanguageIcon
                        className="w-5 h-5 relative z-10"
                        style={{ color: currentTheme.colors.text.accent }}
                      />
                      <span
                        className={`text-sm font-medium relative z-10 ${
                          isRTL ? 'font-cairo' : 'font-sans'
                        }`}
                        style={{ color: currentTheme.colors.text.primary }}
                      >
                        {locale === 'ar' ? 'EN' : 'عر'}
                      </span>
                    </motion.button>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Link href="/login" passHref>
                      <motion.a
                        className={`font-medium transition-all duration-300 ${
                          isRTL ? 'font-tajawal' : 'font-sans'
                        }`}
                        style={{ color: currentTheme.colors.text.accent }}
                        onClick={() => setIsMenuOpen(false)}
                        whileHover={{
                          scale: 1.05,
                          color: currentTheme.colors.text.primary
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        {t('navigation.login')}
                      </motion.a>
                    </Link>

                    <Link href="/signup" passHref>
                      <motion.a
                        className={`relative px-4 py-2 text-sm rounded-xl font-semibold overflow-hidden group ${
                          isRTL ? 'font-cairo' : 'font-display'
                        }`}
                        style={{
                          background: currentTheme.gradients.button,
                          color: 'white',
                          boxShadow: currentTheme.shadows.md,
                          textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                        }}
                        onClick={() => setIsMenuOpen(false)}
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                      >
                        <span className="relative z-10">
                          {t('navigation.joinAsExpert')}
                        </span>
                        {/* Button shimmer effect */}
                        <div
                          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          style={{
                            background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                            animation: 'glassShimmer 1.5s ease-in-out infinite'
                          }}
                        />
                      </motion.a>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
}
