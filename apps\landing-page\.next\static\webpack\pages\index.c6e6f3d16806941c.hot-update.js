"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"../../node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,BriefcaseIcon,LanguageIcon,MoonIcon,SunIcon,XMarkIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { theme: nextTheme, setTheme: setNextTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const { currentTheme, themeName, switchTheme, isGoldTheme, isPurpleTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    // Fix hydration issue with next-themes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // Handle scroll effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Navigation items\n    const navItems = [\n        {\n            key: \"features\",\n            href: \"#features\"\n        },\n        {\n            key: \"howItWorks\",\n            href: \"#how-it-works\"\n        },\n        {\n            key: \"pricing\",\n            href: \"#pricing\"\n        },\n        {\n            key: \"about\",\n            href: \"#about\"\n        },\n        {\n            key: \"contact\",\n            href: \"#contact\"\n        }\n    ];\n    // Language toggle\n    const toggleLanguage = ()=>{\n        const newLocale = locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    // Theme toggle (Next.js theme)\n    const toggleNextTheme = ()=>{\n        setNextTheme(nextTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    // Custom theme toggle (Gold/Purple)\n    const toggleCustomTheme = ()=>{\n        const newTheme = themeName === \"gold\" ? \"purple\" : \"gold\";\n        switchTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500\",\n        style: {\n            background: isScrolled ? \"\".concat(currentTheme.colors.glass.background, \", \").concat(currentTheme.backgrounds.secondary) : \"transparent\",\n            backdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            WebkitBackdropFilter: isScrolled ? currentTheme.colors.glass.backdropBlur : \"none\",\n            borderBottom: isScrolled ? \"1px solid \".concat(currentTheme.colors.glass.border) : \"none\",\n            boxShadow: isScrolled ? currentTheme.colors.glass.shadow : \"none\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto container-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.BriefcaseIcon, {\n                                        className: \"w-5 h-5 lg:w-6 lg:h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl lg:text-2xl font-bold gradient-text\",\n                                    children: isRTL ? \"فريلا سوريا\" : \"Freela Syria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: \"text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-200\",\n                                    onClick: (e)=>{\n                                        var _document_querySelector;\n                                        e.preventDefault();\n                                        (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                            behavior: \"smooth\"\n                                        });\n                                    },\n                                    children: t(\"navigation.\".concat(item.key))\n                                }, item.key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleTheme,\n                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: mounted ? theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.SunIcon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MoonIcon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: toggleLanguage,\n                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 flex items-center space-x-1 rtl:space-x-reverse\",\n                                    \"aria-label\": \"Toggle language\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.LanguageIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    className: \"text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200\",\n                                    children: t(\"navigation.login\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/signup\",\n                                    className: \"btn-primary\",\n                                    children: t(\"navigation.joinAsExpert\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200\",\n                            \"aria-label\": \"Toggle menu\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.XMarkIcon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.Bars3Icon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"lg:hidden overflow-hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4 space-y-4\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.href,\n                                        className: \"block text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-200\",\n                                        onClick: (e)=>{\n                                            var _document_querySelector;\n                                            e.preventDefault();\n                                            setIsMenuOpen(false);\n                                            (_document_querySelector = document.querySelector(item.href)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        children: t(\"navigation.\".concat(item.key))\n                                    }, item.key, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleTheme,\n                                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200\",\n                                                    children: mounted ? theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.SunIcon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MoonIcon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleLanguage,\n                                                    className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 flex items-center space-x-1 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BriefcaseIcon_LanguageIcon_MoonIcon_SunIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.LanguageIcon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: locale === \"ar\" ? \"EN\" : \"عر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"navigation.login\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/signup\",\n                                                    className: \"btn-primary text-sm px-4 py-2\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: t(\"navigation.joinAsExpert\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"P+mAE4n+cOWIcESINwHy1Je9JPo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme,\n        _themes__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Header.tsx\n"));

/***/ })

});